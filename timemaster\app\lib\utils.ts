import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
 
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}


export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Tags utility functions for journal entries
export function tagsToArray(tags: string | string[] | null | undefined): string[] {
  if (!tags) {
    return []
  }

  // If it's already an array, return it
  if (Array.isArray(tags)) {
    return tags.filter(tag => tag && tag.trim().length > 0)
  }

  // If it's a string, split it
  if (tags.trim() === '') {
    return []
  }
  return tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
}

export function arrayToTags(tags: string[]): string {
  return tags.filter(tag => tag.trim().length > 0).join(', ')
}