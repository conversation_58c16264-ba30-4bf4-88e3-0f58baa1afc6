
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  CheckSquare, 
  Plus, 
  Calendar,
  Clock,
  Target,
  Zap,
  AlertTriangle,
  Filter
} from 'lucide-react'
import { Task, Goal } from '@/lib/types'
import { motion } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import { taskSchema, validateFormData, getValidationErrorMessage } from '@/lib/validations'
import { useSearchParams } from 'next/navigation'
import { Skeleton } from '@/components/ui/skeleton'

export default function TasksPage() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [goals, setGoals] = useState<Goal[]>([])
  const [selectedTimeFrame, setSelectedTimeFrame] = useState('DAILY')
  const [loading, setLoading] = useState(true)
  const [showNewTaskForm, setShowNewTaskForm] = useState(false)
  const [filter, setFilter] = useState('all')
  const [showEisenhowerMatrix, setShowEisenhowerMatrix] = useState(false)
  const { toast } = useToast()
  const searchParams = useSearchParams()

  useEffect(() => {
    fetchTasks()
    fetchGoals()
    
    // Check if new task form should be shown
    if (searchParams.get('new') === 'true') {
      setShowNewTaskForm(true)
    }
  }, [selectedTimeFrame, searchParams])

  const fetchTasks = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedTimeFrame !== 'all') {
        params.append('timeFrame', selectedTimeFrame)
      }
      
      const response = await fetch(`/api/tasks?${params}`)
      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      toast({
        title: "Error",
        description: "Failed to fetch tasks",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchGoals = async () => {
    try {
      const response = await fetch('/api/goals?status=ACTIVE')
      if (response.ok) {
        const data = await response.json()
        setGoals(data)
      }
    } catch (error) {
      console.error('Failed to fetch goals:', error)
    }
  }

  const handleCreateTask = async (formData: any) => {
    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const newTask = await response.json()
        setTasks(prev => [newTask, ...prev])
        setShowNewTaskForm(false)
        toast({
          title: "Success",
          description: "Task created successfully"
        })
      }
    } catch (error) {
      console.error('Failed to create task:', error)
      toast({
        title: "Error",
        description: "Failed to create task",
        variant: "destructive"
      })
    }
  }

  const handleTaskToggle = async (taskId: string, completed: boolean) => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status: completed ? 'COMPLETED' : 'PENDING',
          completedAt: completed ? new Date() : null
        })
      })
      
      if (response.ok) {
        setTasks(prev => prev.map(task => 
          task.id === taskId 
            ? { ...task, status: completed ? 'COMPLETED' : 'PENDING' }
            : task
        ))
      }
    } catch (error) {
      console.error('Failed to update task:', error)
      toast({
        title: "Error",
        description: "Failed to update task",
        variant: "destructive"
      })
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'destructive'
      case 'MEDIUM': return 'warning'
      case 'LOW': return 'secondary'
      default: return 'secondary'
    }
  }

  const filteredTasks = tasks.filter(task => {
    if (filter === 'all') return true
    if (filter === 'completed') return task.status === 'COMPLETED'
    if (filter === 'pending') return task.status === 'PENDING'
    if (filter === 'high') return task.priority === 'HIGH'
    return true
  })

  const eisenhowerQuadrants = {
    urgentImportant: filteredTasks.filter(task => task.isUrgent && task.isImportant),
    notUrgentImportant: filteredTasks.filter(task => !task.isUrgent && task.isImportant),
    urgentNotImportant: filteredTasks.filter(task => task.isUrgent && !task.isImportant),
    notUrgentNotImportant: filteredTasks.filter(task => !task.isUrgent && !task.isImportant)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-1/3" />
        <div className="space-y-4">
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
            <CheckSquare className="w-8 h-8 text-primary" />
            <span>Tasks</span>
          </h1>
          <p className="text-muted-foreground mt-2">
            Organize your daily, weekly, and monthly tasks
          </p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            onClick={() => setShowEisenhowerMatrix(!showEisenhowerMatrix)}
          >
            <Target className="w-4 h-4 mr-2" />
            {showEisenhowerMatrix ? 'List View' : 'Matrix View'}
          </Button>
          <Button onClick={() => setShowNewTaskForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            New Task
          </Button>
        </div>
      </div>

      {/* Time Frame and Filter Tabs */}
      <div className="flex items-center justify-between">
        <Tabs value={selectedTimeFrame} onValueChange={setSelectedTimeFrame}>
          <TabsList>
            <TabsTrigger value="DAILY">Daily</TabsTrigger>
            <TabsTrigger value="WEEKLY">Weekly</TabsTrigger>
            <TabsTrigger value="MONTHLY">Monthly</TabsTrigger>
            <TabsTrigger value="all">All Tasks</TabsTrigger>
          </TabsList>
        </Tabs>

        <Select value={filter} onValueChange={setFilter}>
          <SelectTrigger className="w-40">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tasks</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="high">High Priority</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* New Task Form */}
      {showNewTaskForm && (
        <NewTaskForm 
          goals={goals}
          onSubmit={handleCreateTask}
          onCancel={() => setShowNewTaskForm(false)}
        />
      )}

      {/* Tasks Display */}
      {showEisenhowerMatrix ? (
        <EisenhowerMatrix 
          quadrants={eisenhowerQuadrants}
          onTaskToggle={handleTaskToggle}
        />
      ) : (
        <TaskList 
          tasks={filteredTasks}
          onTaskToggle={handleTaskToggle}
        />
      )}
    </div>
  )
}

function NewTaskForm({ goals, onSubmit, onCancel }: {
  goals: Goal[]
  onSubmit: (data: any) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'MEDIUM',
    dueDate: '',
    category: '',
    timeFrame: 'DAILY',
    isRecurring: false,
    recurrencePattern: '',
    isUrgent: false,
    isImportant: true,
    estimatedMinutes: '',
    goalId: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    const data = {
      ...formData,
      dueDate: formData.dueDate ? new Date(formData.dueDate) : null,
      estimatedMinutes: formData.estimatedMinutes ? parseInt(formData.estimatedMinutes) : null,
      goalId: formData.goalId || null
    }

    const validation = validateFormData(taskSchema, data)

    if (!validation.success) {
      setErrors(validation.errors)
      toast({
        title: "Validation Error",
        description: getValidationErrorMessage(validation.errors),
        variant: "destructive"
      })
      setIsSubmitting(false)
      return
    }

    try {
      await onSubmit(validation.data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create task",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Task</CardTitle>
        <CardDescription>
          Add a new task to your list and set priorities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Task Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => {
                setFormData({...formData, title: e.target.value})
                if (errors.title) {
                  setErrors(prev => ({ ...prev, title: '' }))
                }
              }}
              placeholder="e.g., Review quarterly reports"
              className={errors.title ? 'border-destructive' : ''}
            />
            {errors.title && (
              <p className="text-sm text-destructive mt-1">{errors.title}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => {
                setFormData({...formData, description: e.target.value})
                if (errors.description) {
                  setErrors(prev => ({ ...prev, description: '' }))
                }
              }}
              placeholder="Additional details about the task..."
              rows={3}
              className={errors.description ? 'border-destructive' : ''}
            />
            {errors.description && (
              <p className="text-sm text-destructive mt-1">{errors.description}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select 
                value={formData.priority}
                onValueChange={(value) => setFormData({...formData, priority: value})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="timeFrame">Time Frame</Label>
              <Select 
                value={formData.timeFrame}
                onValueChange={(value) => setFormData({...formData, timeFrame: value})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DAILY">Daily</SelectItem>
                  <SelectItem value="WEEKLY">Weekly</SelectItem>
                  <SelectItem value="MONTHLY">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="datetime-local"
                value={formData.dueDate}
                onChange={(e) => setFormData({...formData, dueDate: e.target.value})}
              />
            </div>

            <div>
              <Label htmlFor="estimatedMinutes">Estimated Time (minutes)</Label>
              <Input
                id="estimatedMinutes"
                type="number"
                value={formData.estimatedMinutes}
                onChange={(e) => setFormData({...formData, estimatedMinutes: e.target.value})}
                placeholder="60"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="goalId">Related Goal (Optional)</Label>
            <Select 
              value={formData.goalId}
              onValueChange={(value) => setFormData({...formData, goalId: value})}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a goal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No Goal</SelectItem>
                {goals.map(goal => (
                  <SelectItem key={goal.id} value={goal.id}>
                    {goal.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Eisenhower Matrix Classification */}
          <div className="space-y-3">
            <Label>Task Classification</Label>
            <div className="flex space-x-4">
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={formData.isUrgent}
                  onCheckedChange={(checked) => setFormData({...formData, isUrgent: checked as boolean})}
                />
                <span className="text-sm">Urgent</span>
              </label>
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={formData.isImportant}
                  onCheckedChange={(checked) => setFormData({...formData, isImportant: checked as boolean})}
                />
                <span className="text-sm">Important</span>
              </label>
            </div>
          </div>

          <div className="flex space-x-2 pt-4">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Task'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function TaskList({ tasks, onTaskToggle }: { 
  tasks: Task[]
  onTaskToggle: (id: string, completed: boolean) => void 
}) {
  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <CheckSquare className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
          <p className="text-muted-foreground">
            Create your first task to get started with productivity tracking
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-3">
      {tasks.map((task, index) => (
        <motion.div
          key={task.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <TaskCard task={task} onToggle={onTaskToggle} />
        </motion.div>
      ))}
    </div>
  )
}

function TaskCard({ task, onToggle }: { 
  task: Task
  onToggle: (id: string, completed: boolean) => void 
}) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'destructive'
      case 'MEDIUM': return 'warning'
      case 'LOW': return 'secondary'
      default: return 'secondary'
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <Checkbox
            checked={task.status === 'COMPLETED'}
            onCheckedChange={(checked) => onToggle(task.id, checked as boolean)}
            className="mt-1"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h4 className={`font-semibold truncate ${
                task.status === 'COMPLETED' ? 'line-through text-muted-foreground' : ''
              }`}>
                {task.title}
              </h4>
              <div className="flex space-x-2 ml-2">
                <Badge variant={getPriorityColor(task.priority)}>
                  {task.priority}
                </Badge>
                {task.isUrgent && (
                  <Badge variant="destructive">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Urgent
                  </Badge>
                )}
                {task.isImportant && (
                  <Badge variant="default">
                    <Zap className="w-3 h-3 mr-1" />
                    Important
                  </Badge>
                )}
              </div>
            </div>
            
            {task.description && (
              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                {task.description}
              </p>
            )}
            
            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
              {task.dueDate && (
                <div className="flex items-center space-x-1">
                  <Calendar className="w-3 h-3" />
                  <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                </div>
              )}
              
              {task.estimatedMinutes && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{task.estimatedMinutes}m</span>
                </div>
              )}
              
              {task.goal && (
                <div className="flex items-center space-x-1">
                  <Target className="w-3 h-3" />
                  <span className="truncate">{task.goal.title}</span>
                </div>
              )}
              
              <Badge variant="outline">
                {task.timeFrame}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function EisenhowerMatrix({ quadrants, onTaskToggle }: {
  quadrants: any
  onTaskToggle: (id: string, completed: boolean) => void
}) {
  const QuadrantCard = ({ title, color, tasks, icon: Icon }: any) => (
    <Card className={`border-l-4 border-l-${color}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center space-x-2">
          <Icon className="w-5 h-5" />
          <span>{title}</span>
          <Badge variant="outline">{tasks.length}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {tasks.length === 0 ? (
          <p className="text-sm text-muted-foreground">No tasks in this quadrant</p>
        ) : (
          tasks.slice(0, 5).map((task: Task) => (
            <div key={task.id} className="flex items-center space-x-2 p-2 rounded border">
              <Checkbox
                checked={task.status === 'COMPLETED'}
                onCheckedChange={(checked) => onTaskToggle(task.id, checked as boolean)}
              />
              <span className={`text-sm flex-1 ${
                task.status === 'COMPLETED' ? 'line-through text-muted-foreground' : ''
              }`}>
                {task.title}
              </span>
            </div>
          ))
        )}
        {tasks.length > 5 && (
          <p className="text-xs text-muted-foreground">
            +{tasks.length - 5} more tasks
          </p>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <QuadrantCard
        title="Do First (Urgent & Important)"
        color="red-500"
        tasks={quadrants.urgentImportant}
        icon={AlertTriangle}
      />
      <QuadrantCard
        title="Schedule (Important, Not Urgent)"
        color="blue-500"
        tasks={quadrants.notUrgentImportant}
        icon={Calendar}
      />
      <QuadrantCard
        title="Delegate (Urgent, Not Important)"
        color="yellow-500"
        tasks={quadrants.urgentNotImportant}
        icon={Zap}
      />
      <QuadrantCard
        title="Eliminate (Neither Urgent nor Important)"
        color="gray-500"
        tasks={quadrants.notUrgentNotImportant}
        icon={Target}
      />
    </div>
  )
}
