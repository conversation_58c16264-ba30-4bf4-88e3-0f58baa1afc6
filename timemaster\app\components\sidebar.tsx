
'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  Home,
  Target,
  CheckSquare,
  Timer,
  BarChart3,
  BookOpen,
  Settings
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/',
    icon: Home,
    description: 'Overview of your productivity'
  },
  {
    name: 'Goals',
    href: '/goals',
    icon: Target,
    description: 'Set and track your objectives'
  },
  {
    name: 'Tasks',
    href: '/tasks',
    icon: CheckSquare,
    description: 'Manage daily, weekly & monthly tasks'
  },
  {
    name: 'Focus',
    href: '/focus',
    icon: Timer,
    description: 'Pomodoro timer for concentration'
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: BarChart3,
    description: 'Time tracking & life balance'
  },
  {
    name: 'Journal',
    href: '/journal',
    icon: BookOpen,
    description: 'Notes and reflections'
  }
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-card border-r border-border shadow-sm">
      {/* Logo/Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center space-x-2">
          <Timer className="w-8 h-8 text-primary" />
          <div>
            <h1 className="text-xl font-bold text-foreground">TimeMaster</h1>
            <p className="text-sm text-muted-foreground">Double Your Efficiency</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          const Icon = item.icon
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200',
                isActive 
                  ? 'bg-primary text-primary-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
              )}
            >
              <Icon className="w-5 h-5" />
              <div>
                <div className="font-medium">{item.name}</div>
                <div className="text-xs opacity-75">{item.description}</div>
              </div>
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border">
        <div className="text-xs text-muted-foreground text-center">
          Inspired by Brian Tracy's
          <br />
          "8 Keys to Time Efficiency"
        </div>
      </div>
    </div>
  )
}
