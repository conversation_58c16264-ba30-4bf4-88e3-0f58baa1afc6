
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  console.log('🧹 Clearing existing data...')
  await prisma.focusSession.deleteMany()
  await prisma.timeLog.deleteMany()
  await prisma.lifeBalanceAssessment.deleteMany()
  await prisma.journalEntry.deleteMany()
  await prisma.procrastinationLog.deleteMany()
  await prisma.task.deleteMany()
  await prisma.actionStep.deleteMany()
  await prisma.plan.deleteMany()
  await prisma.milestone.deleteMany()
  await prisma.goal.deleteMany()
  await prisma.setting.deleteMany()

  // Create Goals
  console.log('🎯 Creating goals...')
  const goals = await Promise.all([
    prisma.goal.create({
      data: {
        title: 'Lose 20 Pounds',
        description: 'Get back in shape and improve overall health through diet and exercise',
        category: 'health',
        priority: 'HIGH',
        status: 'ACTIVE',
        deadline: new Date(new Date().getTime() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        targetValue: 20,
        currentValue: 5,
        unit: 'lbs',
        createdAt: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      }
    }),
    prisma.goal.create({
      data: {
        title: 'Learn Spanish',
        description: 'Become conversationally fluent in Spanish for travel and career advancement',
        category: 'education',
        priority: 'MEDIUM',
        status: 'ACTIVE',
        deadline: new Date(new Date().getTime() + 180 * 24 * 60 * 60 * 1000), // 6 months
        targetValue: 100,
        currentValue: 25,
        unit: 'lessons',
        createdAt: new Date(new Date().getTime() - 45 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.goal.create({
      data: {
        title: 'Save $10,000 Emergency Fund',
        description: 'Build a solid financial foundation with 6 months of expenses saved',
        category: 'financial',
        priority: 'HIGH',
        status: 'ACTIVE',
        deadline: new Date(new Date().getTime() + 365 * 24 * 60 * 60 * 1000), // 1 year
        targetValue: 10000,
        currentValue: 3500,
        unit: 'dollars',
        createdAt: new Date(new Date().getTime() - 60 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.goal.create({
      data: {
        title: 'Read 24 Books This Year',
        description: 'Expand knowledge and improve focus through consistent reading',
        category: 'personal',
        priority: 'MEDIUM',
        status: 'ACTIVE',
        deadline: new Date(new Date().getFullYear(), 11, 31), // End of year
        targetValue: 24,
        currentValue: 8,
        unit: 'books',
        createdAt: new Date(new Date().getFullYear(), 0, 1)
      }
    }),
    prisma.goal.create({
      data: {
        title: 'Get Promoted to Senior Developer',
        description: 'Advance career by improving technical skills and taking on leadership responsibilities',
        category: 'career',
        priority: 'HIGH',
        status: 'ACTIVE',
        deadline: new Date(new Date().getTime() + 300 * 24 * 60 * 60 * 1000),
        createdAt: new Date(new Date().getTime() - 20 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.goal.create({
      data: {
        title: 'Run a Half Marathon',
        description: 'Complete a 13.1 mile race to celebrate fitness achievements',
        category: 'health',
        priority: 'LOW',
        status: 'COMPLETED',
        deadline: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000),
        targetValue: 1,
        currentValue: 1,
        unit: 'race',
        createdAt: new Date(new Date().getTime() - 120 * 24 * 60 * 60 * 1000)
      }
    })
  ])

  // Create Milestones
  console.log('🎖️ Creating milestones...')
  await Promise.all([
    // Weight loss milestones
    prisma.milestone.create({
      data: {
        title: 'Lose first 5 pounds',
        description: 'Initial weight loss milestone',
        completed: true,
        completedAt: new Date(new Date().getTime() - 20 * 24 * 60 * 60 * 1000),
        goalId: goals[0].id
      }
    }),
    prisma.milestone.create({
      data: {
        title: 'Establish workout routine',
        description: 'Exercise 3x per week consistently',
        completed: true,
        completedAt: new Date(new Date().getTime() - 15 * 24 * 60 * 60 * 1000),
        goalId: goals[0].id
      }
    }),
    prisma.milestone.create({
      data: {
        title: 'Lose 10 pounds total',
        description: 'Halfway to weight loss goal',
        completed: false,
        deadline: new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000),
        goalId: goals[0].id
      }
    }),
    // Spanish learning milestones
    prisma.milestone.create({
      data: {
        title: 'Complete beginner course',
        description: 'Finish basic Spanish fundamentals',
        completed: true,
        completedAt: new Date(new Date().getTime() - 10 * 24 * 60 * 60 * 1000),
        goalId: goals[1].id
      }
    }),
    prisma.milestone.create({
      data: {
        title: 'Have first conversation',
        description: 'Practice speaking with a native speaker',
        completed: false,
        deadline: new Date(new Date().getTime() + 45 * 24 * 60 * 60 * 1000),
        goalId: goals[1].id
      }
    })
  ])

  // Create Plans with Action Steps
  console.log('📋 Creating plans and action steps...')
  const plans = await Promise.all([
    prisma.plan.create({
      data: {
        title: 'Weight Loss Action Plan',
        description: 'Detailed plan for achieving weight loss goal',
        goalId: goals[0].id
      }
    }),
    prisma.plan.create({
      data: {
        title: 'Spanish Learning Schedule',
        description: 'Structured approach to learning Spanish',
        goalId: goals[1].id
      }
    })
  ])

  await Promise.all([
    // Weight loss action steps
    prisma.actionStep.create({
      data: {
        title: 'Track daily calories',
        description: 'Use app to monitor food intake',
        completed: true,
        order: 1,
        completedAt: new Date(new Date().getTime() - 25 * 24 * 60 * 60 * 1000),
        planId: plans[0].id
      }
    }),
    prisma.actionStep.create({
      data: {
        title: 'Join gym membership',
        description: 'Sign up for local fitness center',
        completed: true,
        order: 2,
        completedAt: new Date(new Date().getTime() - 20 * 24 * 60 * 60 * 1000),
        planId: plans[0].id
      }
    }),
    prisma.actionStep.create({
      data: {
        title: 'Create meal prep routine',
        description: 'Prepare healthy meals for the week',
        completed: false,
        order: 3,
        deadline: new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000),
        planId: plans[0].id
      }
    }),
    // Spanish learning action steps
    prisma.actionStep.create({
      data: {
        title: 'Download language app',
        description: 'Install Duolingo or similar app',
        completed: true,
        order: 1,
        completedAt: new Date(new Date().getTime() - 40 * 24 * 60 * 60 * 1000),
        planId: plans[1].id
      }
    }),
    prisma.actionStep.create({
      data: {
        title: 'Find conversation partner',
        description: 'Join language exchange platform',
        completed: false,
        order: 2,
        deadline: new Date(new Date().getTime() + 14 * 24 * 60 * 60 * 1000),
        planId: plans[1].id
      }
    })
  ])

  // Create Tasks
  console.log('✅ Creating tasks...')
  const today = new Date()
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  const tasks = await Promise.all([
    // Today's tasks
    prisma.task.create({
      data: {
        title: 'Review quarterly budget',
        description: 'Analyze Q3 expenses and plan Q4 budget',
        priority: 'HIGH',
        status: 'PENDING',
        dueDate: new Date(today.setHours(14, 0, 0, 0)),
        category: 'work',
        timeFrame: 'DAILY',
        isUrgent: true,
        isImportant: true,
        estimatedMinutes: 90,
        goalId: goals[2].id // Emergency fund goal
      }
    }),
    prisma.task.create({
      data: {
        title: 'Complete Spanish lesson',
        description: 'Finish lesson 25 on verb conjugations',
        priority: 'MEDIUM',
        status: 'PENDING',
        dueDate: new Date(today.setHours(19, 0, 0, 0)),
        category: 'education',
        timeFrame: 'DAILY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 30,
        goalId: goals[1].id
      }
    }),
    prisma.task.create({
      data: {
        title: 'Go for 30-minute walk',
        description: 'Get some fresh air and light exercise',
        priority: 'MEDIUM',
        status: 'COMPLETED',
        dueDate: new Date(today.setHours(17, 0, 0, 0)),
        completedAt: new Date(today.setHours(17, 30, 0, 0)),
        category: 'health',
        timeFrame: 'DAILY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 30,
        actualMinutes: 35,
        goalId: goals[0].id
      }
    }),
    prisma.task.create({
      data: {
        title: 'Read chapter 5 of "Atomic Habits"',
        description: 'Continue reading productivity book',
        priority: 'LOW',
        status: 'PENDING',
        dueDate: new Date(today.setHours(21, 0, 0, 0)),
        category: 'personal',
        timeFrame: 'DAILY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 45,
        goalId: goals[3].id
      }
    }),
    // Tomorrow's tasks
    prisma.task.create({
      data: {
        title: 'Team standup meeting',
        description: 'Daily team sync and sprint planning',
        priority: 'HIGH',
        status: 'PENDING',
        dueDate: new Date(tomorrow.setHours(9, 0, 0, 0)),
        category: 'work',
        timeFrame: 'DAILY',
        isUrgent: true,
        isImportant: true,
        estimatedMinutes: 30
      }
    }),
    prisma.task.create({
      data: {
        title: 'Grocery shopping',
        description: 'Buy healthy foods for meal prep',
        priority: 'MEDIUM',
        status: 'PENDING',
        dueDate: new Date(tomorrow.setHours(10, 0, 0, 0)),
        category: 'personal',
        timeFrame: 'WEEKLY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 60,
        goalId: goals[0].id
      }
    }),
    // Weekly tasks
    prisma.task.create({
      data: {
        title: 'Plan next week\'s workouts',
        description: 'Schedule gym sessions and outdoor activities',
        priority: 'MEDIUM',
        status: 'PENDING',
        category: 'health',
        timeFrame: 'WEEKLY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 20,
        goalId: goals[0].id
      }
    }),
    prisma.task.create({
      data: {
        title: 'Review investment portfolio',
        description: 'Check account balances and rebalance if needed',
        priority: 'MEDIUM',
        status: 'PENDING',
        category: 'financial',
        timeFrame: 'WEEKLY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 45,
        goalId: goals[2].id
      }
    }),
    // Monthly tasks
    prisma.task.create({
      data: {
        title: 'Deep clean home office',
        description: 'Organize desk, file documents, clean equipment',
        priority: 'LOW',
        status: 'PENDING',
        category: 'personal',
        timeFrame: 'MONTHLY',
        isUrgent: false,
        isImportant: false,
        estimatedMinutes: 120
      }
    }),
    prisma.task.create({
      data: {
        title: 'Career development plan review',
        description: 'Assess progress and update professional goals',
        priority: 'HIGH',
        status: 'PENDING',
        category: 'career',
        timeFrame: 'MONTHLY',
        isUrgent: false,
        isImportant: true,
        estimatedMinutes: 90,
        goalId: goals[4].id
      }
    })
  ])

  // Create Focus Sessions
  console.log('🎯 Creating focus sessions...')
  await Promise.all([
    prisma.focusSession.create({
      data: {
        duration: 25,
        sessionType: 'FOCUS',
        startedAt: new Date(yesterday.setHours(9, 0, 0, 0)),
        completedAt: new Date(yesterday.setHours(9, 25, 0, 0)),
        notes: 'Worked on quarterly budget analysis - made good progress',
        taskId: tasks[0].id
      }
    }),
    prisma.focusSession.create({
      data: {
        duration: 5,
        sessionType: 'SHORT_BREAK',
        startedAt: new Date(yesterday.setHours(9, 25, 0, 0)),
        completedAt: new Date(yesterday.setHours(9, 30, 0, 0))
      }
    }),
    prisma.focusSession.create({
      data: {
        duration: 25,
        sessionType: 'FOCUS',
        startedAt: new Date(yesterday.setHours(9, 30, 0, 0)),
        completedAt: new Date(yesterday.setHours(9, 55, 0, 0)),
        notes: 'Spanish lesson - learned 15 new vocabulary words',
        taskId: tasks[1].id
      }
    }),
    prisma.focusSession.create({
      data: {
        duration: 25,
        sessionType: 'FOCUS',
        startedAt: new Date(today.setHours(10, 0, 0, 0)),
        completedAt: new Date(today.setHours(10, 25, 0, 0)),
        notes: 'Reading session - finished chapter 4 of Atomic Habits'
      }
    }),
    prisma.focusSession.create({
      data: {
        duration: 15,
        sessionType: 'LONG_BREAK',
        startedAt: new Date(today.setHours(10, 25, 0, 0)),
        completedAt: new Date(today.setHours(10, 40, 0, 0))
      }
    })
  ])

  // Create Time Logs
  console.log('⏰ Creating time logs...')
  await Promise.all([
    // Yesterday's time logs
    prisma.timeLog.create({
      data: {
        activity: 'Code review and development',
        category: 'work',
        duration: 240,
        date: yesterday,
        notes: 'Productive coding session - fixed 3 bugs and implemented new feature'
      }
    }),
    prisma.timeLog.create({
      data: {
        activity: 'Gym workout',
        category: 'health',
        duration: 60,
        date: yesterday,
        notes: 'Full body strength training - felt great!'
      }
    }),
    prisma.timeLog.create({
      data: {
        activity: 'Spanish study',
        category: 'learning',
        duration: 45,
        date: yesterday,
        notes: 'Focused on verb conjugations and practiced speaking'
      }
    }),
    prisma.timeLog.create({
      data: {
        activity: 'Reading',
        category: 'personal',
        duration: 60,
        date: yesterday,
        notes: 'Read 2 chapters of productivity book'
      }
    }),
    prisma.timeLog.create({
      data: {
        activity: 'Social media',
        category: 'leisure',
        duration: 45,
        date: yesterday,
        notes: 'Caught up with friends and news - maybe too much time here'
      }
    }),
    // Today's time logs
    prisma.timeLog.create({
      data: {
        activity: 'Morning routine',
        category: 'personal',
        duration: 30,
        date: today,
        notes: 'Meditation, journaling, and planning the day'
      }
    }),
    prisma.timeLog.create({
      data: {
        activity: 'Project planning meeting',
        category: 'work',
        duration: 90,
        date: today,
        notes: 'Discussed Q4 roadmap and resource allocation'
      }
    }),
    prisma.timeLog.create({
      data: {
        activity: 'Lunch and walk',
        category: 'health',
        duration: 45,
        date: today,
        notes: 'Healthy salad and 20-minute walk around the neighborhood'
      }
    })
  ])

  // Create Life Balance Assessment
  console.log('⚖️ Creating life balance assessment...')
  await prisma.lifeBalanceAssessment.create({
    data: {
      career: 7,
      finances: 6,
      health: 8,
      family: 7,
      social: 5,
      personal: 8,
      recreation: 6,
      environment: 7,
      careerTarget: 9,
      financesTarget: 8,
      healthTarget: 9,
      familyTarget: 8,
      socialTarget: 7,
      personalTarget: 9,
      recreationTarget: 8,
      environmentTarget: 8,
      notes: 'Overall feeling balanced but want to improve social connections and financial security',
      date: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000) // 1 week ago
    }
  })

  // Create Journal Entries
  console.log('📝 Creating journal entries...')
  await Promise.all([
    prisma.journalEntry.create({
      data: {
        title: 'Reflection on Weight Loss Progress',
        content: `I'm really proud of the progress I've made so far. Losing 5 pounds might not seem like much, but it represents a real shift in my mindset and habits. The gym routine is becoming second nature, and I'm actually enjoying the healthier meals I've been preparing.

The hardest part has been staying consistent on weekends, but I'm learning that it's okay to have some flexibility as long as I get back on track. This journey is teaching me patience and self-compassion.

Next week, I want to focus on meal prep to make weekdays easier. I think preparing everything on Sunday will help me stay on track when work gets busy.`,
        mood: 'proud',
        tags: 'health,progress,reflection',
        date: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000),
        goalId: goals[0].id
      }
    }),
    prisma.journalEntry.create({
      data: {
        title: 'Spanish Learning Breakthrough',
        content: `Had an amazing moment today during my Spanish lesson. I was listening to a podcast in Spanish and actually understood about 60% of what they were saying! It was such a rush to realize how much progress I've made.

I remember when I first started, every word sounded like gibberish. Now I can pick out verbs, understand the general topic, and even catch some humor. It's incredible how the brain adapts and learns.

This gives me so much motivation to keep going. I think I'm ready to start looking for conversation partners. The app has been great, but I need real practice with real people.`,
        mood: 'excited',
        tags: 'learning,breakthrough,motivation', // Changed from array to comma-separated string
        date: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000),
        goalId: goals[1].id
      }
    }),
    prisma.journalEntry.create({
      data: {
        title: 'Productivity Thoughts',
        content: `Been thinking a lot about productivity and time management lately. Reading "Atomic Habits" has really shifted my perspective on how change happens. It's not about massive overhauls, but tiny consistent improvements.

I've been tracking my time more carefully, and I'm shocked at how much time I spend on social media. It's not that social media is bad, but I want to be more intentional about it. Maybe I can set specific times for checking it instead of mindlessly scrolling throughout the day.

Also realizing that my energy levels are highest in the morning, so I should tackle the most important work then. Afternoons are better for meetings and admin tasks.

Goal for next week: Morning routine that includes the most important task of the day before checking any messages.`,
        mood: 'focused',
        tags: 'productivity,habits,time-management,insights',
        date: new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.journalEntry.create({
      data: {
        title: 'Weekend Planning',
        content: `Weekends have been challenging for maintaining my routines. During the week, I have structure and momentum, but weekends feel chaotic. I want to enjoy my free time while still making progress on my goals.

Maybe the answer is to plan one productive thing for Saturday morning - like meal prep or a good workout - and then let the rest of the weekend be more flexible. That way I maintain some momentum without feeling like I'm always "on."

I also want to make sure I'm spending quality time with friends and family. Success isn't just about achieving goals; it's about enjoying the journey and maintaining relationships.`,
        mood: 'thoughtful',
        tags: 'planning,balance,weekends', // Changed from array to comma-separated string
        date: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.journalEntry.create({
      data: {
        title: 'Gratitude Practice',
        content: `Taking a moment to appreciate what's going well in my life:

1. My health is improving - I have more energy and feel stronger
2. Learning Spanish is opening my mind to new cultures and ways of thinking  
3. My emergency fund is growing, which gives me peace of mind
4. I have supportive friends and family who encourage my goals
5. My job is challenging but rewarding, and I'm learning a lot

Sometimes when I'm focused on what I want to achieve, I forget to appreciate what I already have. This gratitude practice helps me stay grounded and positive.

Even on difficult days, there's always something to be thankful for. This perspective shift has been really powerful for my mental health and motivation.`,
        mood: 'grateful',
        tags: 'gratitude,reflection,positivity',
        date: new Date()
      }
    })
  ])

  // Create some Procrastination Logs
  console.log('😅 Creating procrastination logs...')
  await Promise.all([
    prisma.procrastinationLog.create({
      data: {
        taskTitle: 'Review investment portfolio',
        reason: 'overwhelming',
        strategy: 'Broke it down into smaller steps: check balances, research new options, make adjustments',
        timeWasted: 45,
        date: new Date(new Date().getTime() - 4 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.procrastinationLog.create({
      data: {
        taskTitle: 'Deep clean home office',
        reason: 'boring',
        strategy: 'Put on energizing music and set 25-minute timer to make it more enjoyable',
        timeWasted: 30,
        date: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)
      }
    }),
    prisma.procrastinationLog.create({
      data: {
        taskTitle: 'Career development plan review',
        reason: 'unclear',
        strategy: 'Found a template online and scheduled specific time block for it',
        timeWasted: 60,
        date: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000)
      }
    })
  ])

  // Create Settings
  console.log('⚙️ Creating settings...')
  await Promise.all([
    prisma.setting.create({
      data: {
        key: 'pomodoro_focus_duration',
        value: '25'
      }
    }),
    prisma.setting.create({
      data: {
        key: 'pomodoro_short_break',
        value: '5'
      }
    }),
    prisma.setting.create({
      data: {
        key: 'pomodoro_long_break',
        value: '15'
      }
    }),
    prisma.setting.create({
      data: {
        key: 'theme',
        value: 'light'
      }
    })
  ])

  console.log('✅ Database seeding completed successfully!')
  console.log(`
📊 Created:
  • ${goals.length} Goals (with milestones and plans)
  • ${tasks.length} Tasks (across different time frames)
  • 5 Focus Sessions
  • 8 Time Logs
  • 1 Life Balance Assessment
  • 5 Journal Entries
  • 3 Procrastination Logs
  • 4 Settings

🚀 TimeMaster is ready to demo all features!
  `)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
