
// Database types based on Prisma schema
export type Priority = 'LOW' | 'MEDIUM' | 'HIGH'
export type GoalStatus = 'ACTIVE' | 'COMPLETED' | 'PAUSED' | 'CANCELLED'
export type TaskStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
export type TimeFrame = 'DAILY' | 'WEEKLY' | 'MONTHLY'
export type SessionType = 'FOCUS' | 'SHORT_BREAK' | 'LONG_BREAK'

export interface Goal {
  id: string
  title: string
  description?: string
  category: string
  priority: Priority
  status: GoalStatus
  deadline?: Date
  targetValue?: number
  currentValue?: number
  unit?: string
  createdAt: Date
  updatedAt: Date
  milestones?: Milestone[]
  plans?: Plan[]
  tasks?: Task[]
}

export interface Milestone {
  id: string
  title: string
  description?: string
  completed: boolean
  deadline?: Date
  completedAt?: Date
  createdAt: Date
  goalId: string
}

export interface Plan {
  id: string
  title: string
  description?: string
  createdAt: Date
  updatedAt: Date
  goalId: string
  actionSteps?: ActionStep[]
}

export interface ActionStep {
  id: string
  title: string
  description?: string
  completed: boolean
  order: number
  deadline?: Date
  completedAt?: Date
  createdAt: Date
  planId: string
}

export interface Task {
  id: string
  title: string
  description?: string
  priority: Priority
  status: TaskStatus
  dueDate?: Date
  completedAt?: Date
  createdAt: Date
  updatedAt: Date
  category?: string
  timeFrame: TimeFrame
  isRecurring: boolean
  recurrencePattern?: string
  isUrgent: boolean
  isImportant: boolean
  estimatedMinutes?: number
  actualMinutes?: number
  goalId?: string
  goal?: Goal
}

export interface FocusSession {
  id: string
  duration: number
  sessionType: SessionType
  startedAt: Date
  completedAt?: Date
  notes?: string
  createdAt: Date
  taskId?: string
  task?: Task
}

export interface TimeLog {
  id: string
  activity: string
  category: string
  duration: number
  date: Date
  notes?: string
  createdAt: Date
}

export interface LifeBalanceAssessment {
  id: string
  date: Date
  career: number
  finances: number
  health: number
  family: number
  social: number
  personal: number
  recreation: number
  environment: number
  careerTarget: number
  financesTarget: number
  healthTarget: number
  familyTarget: number
  socialTarget: number
  personalTarget: number
  recreationTarget: number
  environmentTarget: number
  notes?: string
  createdAt: Date
}

export interface JournalEntry {
  id: string
  title?: string
  content: string
  date: Date
  mood?: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  goalId?: string
  taskId?: string
}

export interface ProcrastinationLog {
  id: string
  taskTitle: string
  reason: string
  strategy?: string
  timeWasted?: number
  date: Date
  createdAt: Date
}

// UI Component types
export interface DashboardStats {
  totalGoals: number
  activeGoals: number
  completedGoals: number
  todayTasks: number
  completedTasks: number
  focusMinutes: number
  lifeBalanceScore: number
}

export interface TimerState {
  minutes: number
  seconds: number
  isRunning: boolean
  sessionType: SessionType
  sessionCount: number
}

export interface LifeArea {
  name: string
  current: number
  target: number
  key: keyof Omit<LifeBalanceAssessment, 'id' | 'date' | 'notes' | 'createdAt'>
}
