
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const category = searchParams.get('category')
    const limit = searchParams.get('limit')

    const where: any = {}
    
    if (status) {
      where.status = status
    }
    
    if (category) {
      where.category = category
    }

    const goals = await prisma.goal.findMany({
      where,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit ? parseInt(limit) : undefined,
      include: {
        milestones: true,
        plans: { include: { actionSteps: true } },
        tasks: { where: { status: { not: 'CANCELLED' } } },
        _count: {
          select: {
            milestones: true,
            plans: true,
            tasks: true
          }
        }
      }
    })

    return NextResponse.json(goals)
  } catch (error) {
    console.error('Get goals error:', error)
    return NextResponse.json({ error: 'Failed to fetch goals' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const goal = await prisma.goal.create({
      data: {
        title: data.title,
        description: data.description,
        category: data.category || 'personal',
        priority: data.priority || 'MEDIUM',
        status: 'ACTIVE',
        deadline: data.deadline ? new Date(data.deadline) : null,
        targetValue: data.targetValue,
        currentValue: data.currentValue || 0,
        unit: data.unit
      },
      include: {
        milestones: true,
        plans: { include: { actionSteps: true } },
        tasks: true,
        _count: {
          select: {
            milestones: true,
            plans: true,
            tasks: true
          }
        }
      }
    })

    return NextResponse.json(goal, { status: 201 })
  } catch (error) {
    console.error('Create goal error:', error)
    return NextResponse.json({ error: 'Failed to create goal' }, { status: 500 })
  }
}
