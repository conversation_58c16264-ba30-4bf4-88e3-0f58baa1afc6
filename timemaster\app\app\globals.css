
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 210 20% 20%;
    --card: 210 40% 94%;
    --card-foreground: 210 20% 20%;
    --popover: 210 40% 98%;
    --popover-foreground: 210 20% 20%;
    --primary: 210 90% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 94%;
    --secondary-foreground: 210 20% 20%;
    --muted: 210 40% 94%;
    --muted-foreground: 210 20% 60%;
    --accent: 210 40% 94%;
    --accent-foreground: 210 20% 20%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 210 40% 90%;
    --input: 210 40% 90%;
    --ring: 210 90% 60%;
    --radius: 0.5rem;
    --success: 145 63% 49%;
    --warning: 38 92% 50%;
  }

  .dark {
    --background: 210 20% 8%;
    --foreground: 210 40% 98%;
    --card: 210 20% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 210 20% 8%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 90% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 20% 12%;
    --secondary-foreground: 210 40% 98%;
    --muted: 210 20% 12%;
    --muted-foreground: 210 40% 60%;
    --accent: 210 20% 12%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 210 20% 20%;
    --input: 210 20% 20%;
    --ring: 210 90% 60%;
    --success: 145 63% 49%;
    --warning: 38 92% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

/* Custom scrollbar */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Animation for counting numbers */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.count-up {
  animation: countUp 0.5s ease-out;
}

/* Focus timer styles */
.timer-ring {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}

.timer-progress {
  transition: stroke-dasharray 1s linear;
}

/* Life balance wheel */
.balance-segment {
  transition: all 0.3s ease;
}

.balance-segment:hover {
  opacity: 0.8;
  transform: scale(1.05);
}
