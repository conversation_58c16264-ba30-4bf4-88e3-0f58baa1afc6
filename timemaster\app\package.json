{"name": "app", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "tsx --require dotenv/config scripts/seed.ts"}, "prisma": {"seed": "tsx --require dotenv/config scripts/seed.ts"}, "devDependencies": {"@next/swc-wasm-nodejs": "15.4.0", "@types/node": "24.0.13", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@typescript-eslint/eslint-plugin": "8.36.0", "@typescript-eslint/parser": "8.36.0", "eslint": "9.24.0", "eslint-config-next": "15.3.0", "eslint-plugin-prettier": "5.5.1", "eslint-plugin-react-hooks": "4.6.0", "postcss": "8.4.30", "prisma": "6.7.0", "tailwind-merge": "3.3.1", "tailwindcss": "3.3.3", "tailwindcss-animate": "1.0.7", "ts-node": "10.9.2", "tsx": "4.20.3", "typescript": "5.8.3"}, "dependencies": {"@hookform/resolvers": "5.1.1", "@next-auth/prisma-adapter": "1.0.7", "@prisma/client": "6.11.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@types/bcryptjs": "2.4.6", "@types/jsonwebtoken": "9.0.10", "autoprefixer": "10.4.15", "bcryptjs": "3.0.2", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "dayjs": "1.11.13", "dotenv": "17.2.0", "framer-motion": "12.23.3", "jsonwebtoken": "9.0.2", "lucide-react": "0.446.0", "next": "15.3.5", "next-auth": "4.24.11", "next-themes": "0.4.6", "react": "19.1.0", "react-day-picker": "9.8.0", "react-dom": "19.1.0", "react-hook-form": "7.60.0", "react-hot-toast": "2.5.2", "react-resizable-panels": "3.0.3", "recharts": "2.1.16", "sonner": "2.0.6", "tailwind-scrollbar-hide": "4.0.0", "webpack": "5.100.1", "zod": "4.0.5", "zustand": "5.0.3"}, "browserslist": ["ie >= 11", "> 0.5%", "not dead", "last 2 versions"]}