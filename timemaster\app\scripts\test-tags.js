// Test script for tags utility functions
const { tagsToArray, arrayToTags } = require('../lib/utils.ts');

console.log('🧪 Testing Tags Utility Functions\n');

// Test data from the actual journal entries
const testCases = [
  'gratitude,reflection,positivity',
  'productivity,habits,time-management,insights',
  'planning,balance,weekends',
  'learning,breakthrough,motivation',
  '',
  null,
  undefined,
  'single-tag',
  'tag1, tag2, tag3',
  '  spaced  ,  tags  ,  with  ,  spaces  '
];

console.log('📋 Test Results:');
console.log('================');

testCases.forEach((testCase, index) => {
  console.log(`\nTest ${index + 1}: "${testCase}"`);
  
  try {
    const array = tagsToArray(testCase);
    const backToString = arrayToTags(array);
    
    console.log(`  → Array: [${array.map(tag => `"${tag}"`).join(', ')}]`);
    console.log(`  → Back to string: "${backToString}"`);
    console.log(`  ✅ Success`);
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
  }
});

console.log('\n🎯 Summary:');
console.log('- tagsToArray: Converts comma-separated strings to arrays');
console.log('- arrayToTags: Converts arrays back to comma-separated strings');
console.log('- Handles null, undefined, and empty strings gracefully');
console.log('- Trims whitespace and filters empty tags');
console.log('\n✅ Tags utility functions are working correctly!');