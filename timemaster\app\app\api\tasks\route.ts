
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeFrame = searchParams.get('timeFrame')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const limit = searchParams.get('limit')

    const where: any = {}
    
    if (timeFrame) {
      where.timeFrame = timeFrame
    }
    
    if (status) {
      where.status = status
    }
    
    if (priority) {
      where.priority = priority
    }

    const tasks = await prisma.task.findMany({
      where,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit ? parseInt(limit) : undefined,
      include: {
        goal: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(tasks)
  } catch (error) {
    console.error('Get tasks error:', error)
    return NextResponse.json({ error: 'Failed to fetch tasks' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const task = await prisma.task.create({
      data: {
        title: data.title,
        description: data.description,
        priority: data.priority || 'MEDIUM',
        status: 'PENDING',
        dueDate: data.dueDate ? new Date(data.dueDate) : null,
        category: data.category,
        timeFrame: data.timeFrame || 'DAILY',
        isRecurring: data.isRecurring || false,
        recurrencePattern: data.recurrencePattern,
        isUrgent: data.isUrgent || false,
        isImportant: data.isImportant || true,
        estimatedMinutes: data.estimatedMinutes,
        goalId: data.goalId
      },
      include: {
        goal: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(task, { status: 201 })
  } catch (error) {
    console.error('Create task error:', error)
    return NextResponse.json({ error: 'Failed to create task' }, { status: 500 })
  }
}
