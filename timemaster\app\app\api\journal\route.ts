
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = searchParams.get('limit')

    const entries = await prisma.journalEntry.findMany({
      orderBy: { createdAt: 'desc' },
      take: limit ? parseInt(limit) : undefined,
      include: {
        goal: { select: { id: true, title: true } },
        task: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(entries)
  } catch (error) {
    console.error('Get journal entries error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch journal entries' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const entry = await prisma.journalEntry.create({
      data: {
        title: data.title,
        content: data.content,
        mood: data.mood,
        tags: data.tags || null, // tags should be a string or null
        goalId: data.goalId,
        taskId: data.taskId
      },
      include: {
        goal: { select: { id: true, title: true } },
        task: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(entry, { status: 201 })
  } catch (error) {
    console.error('Create journal entry error:', error)
    return NextResponse.json(
      { error: 'Failed to create journal entry' },
      { status: 500 }
    )
  }
}
