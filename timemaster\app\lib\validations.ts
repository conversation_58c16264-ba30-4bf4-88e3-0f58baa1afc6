import { z } from 'zod'

// Goal validation schema
export const goalSchema = z.object({
  title: z.string()
    .min(1, 'Goal title is required')
    .max(100, 'Goal title must be less than 100 characters'),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  category: z.enum(['personal', 'professional', 'health', 'financial', 'education', 'relationships'], {
    errorMap: () => ({ message: 'Please select a valid category' })
  }),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH'], {
    errorMap: () => ({ message: 'Please select a valid priority' })
  }),
  deadline: z.date()
    .min(new Date(), 'Deadline must be in the future')
    .optional()
    .nullable(),
  targetValue: z.number()
    .positive('Target value must be positive')
    .optional()
    .nullable(),
  unit: z.string()
    .max(20, 'Unit must be less than 20 characters')
    .optional()
})

// Task validation schema
export const taskSchema = z.object({
  title: z.string()
    .min(1, 'Task title is required')
    .max(100, 'Task title must be less than 100 characters'),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH'], {
    errorMap: () => ({ message: 'Please select a valid priority' })
  }),
  dueDate: z.date()
    .min(new Date(), 'Due date must be in the future')
    .optional()
    .nullable(),
  category: z.string()
    .max(50, 'Category must be less than 50 characters')
    .optional(),
  timeFrame: z.enum(['DAILY', 'WEEKLY', 'MONTHLY'], {
    errorMap: () => ({ message: 'Please select a valid time frame' })
  }),
  isRecurring: z.boolean(),
  recurrencePattern: z.string()
    .max(100, 'Recurrence pattern must be less than 100 characters')
    .optional(),
  isUrgent: z.boolean(),
  isImportant: z.boolean(),
  estimatedMinutes: z.number()
    .positive('Estimated time must be positive')
    .max(1440, 'Estimated time cannot exceed 24 hours (1440 minutes)')
    .optional()
    .nullable(),
  goalId: z.string().optional().nullable()
})

// Journal Entry validation schema
export const journalEntrySchema = z.object({
  title: z.string()
    .max(100, 'Title must be less than 100 characters')
    .optional(),
  content: z.string()
    .min(1, 'Content is required')
    .max(5000, 'Content must be less than 5000 characters'),
  mood: z.enum(['happy', 'sad', 'excited', 'anxious', 'calm', 'frustrated', 'motivated', 'tired'], {
    errorMap: () => ({ message: 'Please select a valid mood' })
  }).optional(),
  tags: z.string()
    .max(200, 'Tags must be less than 200 characters')
    .optional(),
  goalId: z.string().optional().nullable(),
  taskId: z.string().optional().nullable()
})

// Time Log validation schema
export const timeLogSchema = z.object({
  activity: z.string()
    .min(1, 'Activity is required')
    .max(100, 'Activity must be less than 100 characters'),
  category: z.string()
    .min(1, 'Category is required')
    .max(50, 'Category must be less than 50 characters'),
  duration: z.number()
    .positive('Duration must be positive')
    .max(1440, 'Duration cannot exceed 24 hours (1440 minutes)'),
  date: z.date(),
  notes: z.string()
    .max(500, 'Notes must be less than 500 characters')
    .optional()
})

// Life Balance Assessment validation schema
export const lifeBalanceSchema = z.object({
  career: z.number()
    .min(1, 'Career score must be between 1 and 10')
    .max(10, 'Career score must be between 1 and 10'),
  finances: z.number()
    .min(1, 'Finances score must be between 1 and 10')
    .max(10, 'Finances score must be between 1 and 10'),
  health: z.number()
    .min(1, 'Health score must be between 1 and 10')
    .max(10, 'Health score must be between 1 and 10'),
  family: z.number()
    .min(1, 'Family score must be between 1 and 10')
    .max(10, 'Family score must be between 1 and 10'),
  social: z.number()
    .min(1, 'Social score must be between 1 and 10')
    .max(10, 'Social score must be between 1 and 10'),
  personal: z.number()
    .min(1, 'Personal score must be between 1 and 10')
    .max(10, 'Personal score must be between 1 and 10'),
  recreation: z.number()
    .min(1, 'Recreation score must be between 1 and 10')
    .max(10, 'Recreation score must be between 1 and 10'),
  environment: z.number()
    .min(1, 'Environment score must be between 1 and 10')
    .max(10, 'Environment score must be between 1 and 10'),
  careerTarget: z.number()
    .min(1, 'Career target must be between 1 and 10')
    .max(10, 'Career target must be between 1 and 10'),
  financesTarget: z.number()
    .min(1, 'Finances target must be between 1 and 10')
    .max(10, 'Finances target must be between 1 and 10'),
  healthTarget: z.number()
    .min(1, 'Health target must be between 1 and 10')
    .max(10, 'Health target must be between 1 and 10'),
  familyTarget: z.number()
    .min(1, 'Family target must be between 1 and 10')
    .max(10, 'Family target must be between 1 and 10'),
  socialTarget: z.number()
    .min(1, 'Social target must be between 1 and 10')
    .max(10, 'Social target must be between 1 and 10'),
  personalTarget: z.number()
    .min(1, 'Personal target must be between 1 and 10')
    .max(10, 'Personal target must be between 1 and 10'),
  recreationTarget: z.number()
    .min(1, 'Recreation target must be between 1 and 10')
    .max(10, 'Recreation target must be between 1 and 10'),
  environmentTarget: z.number()
    .min(1, 'Environment target must be between 1 and 10')
    .max(10, 'Environment target must be between 1 and 10'),
  notes: z.string()
    .max(1000, 'Notes must be less than 1000 characters')
    .optional()
})

// Helper function to validate form data and return formatted errors
export function validateFormData<T>(schema: z.ZodSchema<T>, data: unknown) {
  const result = schema.safeParse(data)

  if (!result.success) {
    const errors: Record<string, string> = {}
    result.error.errors.forEach((error) => {
      const path = error.path.join('.')
      errors[path] = error.message
    })
    return { success: false, errors, data: null }
  }

  return { success: true, errors: {}, data: result.data }
}

// Helper function to format validation errors for toast notifications
export function getValidationErrorMessage(errors: Record<string, string>): string {
  const errorMessages = Object.values(errors)
  if (errorMessages.length === 1) {
    return errorMessages[0]
  }
  return `Please fix the following errors: ${errorMessages.slice(0, 3).join(', ')}${errorMessages.length > 3 ? '...' : ''}`
}

// Export types for TypeScript
export type GoalFormData = z.infer<typeof goalSchema>
export type TaskFormData = z.infer<typeof taskSchema>
export type JournalEntryFormData = z.infer<typeof journalEntrySchema>
export type TimeLogFormData = z.infer<typeof timeLogSchema>
export type LifeBalanceFormData = z.infer<typeof lifeBalanceSchema>
