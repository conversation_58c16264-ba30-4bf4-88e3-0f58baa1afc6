
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = searchParams.get('limit')
    const category = searchParams.get('category')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const where: any = {}
    
    if (category) {
      where.category = category
    }
    
    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const timeLogs = await prisma.timeLog.findMany({
      where,
      orderBy: { date: 'desc' },
      take: limit ? parseInt(limit) : undefined
    })

    return NextResponse.json(timeLogs)
  } catch (error) {
    console.error('Get time logs error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch time logs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const timeLog = await prisma.timeLog.create({
      data: {
        activity: data.activity,
        category: data.category,
        duration: data.duration,
        date: new Date(data.date),
        notes: data.notes
      }
    })

    return NextResponse.json(timeLog, { status: 201 })
  } catch (error) {
    console.error('Create time log error:', error)
    return NextResponse.json(
      { error: 'Failed to create time log' },
      { status: 500 }
    )
  }
}
