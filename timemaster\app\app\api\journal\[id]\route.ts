
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const entry = await prisma.journalEntry.findUnique({
      where: { id: params.id },
      include: {
        goal: { select: { id: true, title: true } },
        task: { select: { id: true, title: true } }
      }
    })

    if (!entry) {
      return NextResponse.json({ error: 'Journal entry not found' }, { status: 404 })
    }

    return NextResponse.json(entry)
  } catch (error) {
    console.error('Get journal entry error:', error)
    return NextResponse.json({ error: 'Failed to fetch journal entry' }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json()
    
    const entry = await prisma.journalEntry.update({
      where: { id: params.id },
      data: {
        title: data.title,
        content: data.content,
        mood: data.mood,
        tags: typeof data.tags === 'string' ? data.tags : null,
        goalId: data.goalId,
        taskId: data.taskId
      },
      include: {
        goal: { select: { id: true, title: true } },
        task: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(entry)
  } catch (error) {
    console.error('Update journal entry error:', error)
    return NextResponse.json({ error: 'Failed to update journal entry' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.journalEntry.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete journal entry error:', error)
    return NextResponse.json({ error: 'Failed to delete journal entry' }, { status: 500 })
  }
}
