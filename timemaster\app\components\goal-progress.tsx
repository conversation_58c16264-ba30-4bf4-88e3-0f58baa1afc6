
'use client'

import { useEffect, useState } from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Target, ArrowRight } from 'lucide-react'
import { Goal } from '@/lib/types'
import { motion } from 'framer-motion'
import Link from 'next/link'

export function GoalProgress() {
  const [goals, setGoals] = useState<Goal[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchActiveGoals()
  }, [])

  const fetchActiveGoals = async () => {
    try {
      const response = await fetch('/api/goals?status=ACTIVE&limit=3')
      if (response.ok) {
        const data = await response.json()
        setGoals(data)
      }
    } catch (error) {
      console.error('Failed to fetch active goals:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateProgress = (goal: Goal) => {
    if (!goal.targetValue || !goal.currentValue) return 0
    return Math.min((goal.currentValue / goal.targetValue) * 100, 100)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'destructive'
      case 'MEDIUM': return 'warning'
      case 'LOW': return 'secondary'
      default: return 'secondary'
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="p-4 border rounded-lg animate-pulse">
            <div className="h-4 bg-muted rounded mb-2"></div>
            <div className="h-2 bg-muted rounded mb-2"></div>
            <div className="h-3 bg-muted rounded w-1/3"></div>
          </div>
        ))}
      </div>
    )
  }

  if (goals.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
          <Target className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="font-semibold mb-2">No active goals</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Set your first goal to start tracking progress
        </p>
        <Link href="/goals?new=true">
          <Button size="sm">
            <Target className="w-4 h-4 mr-2" />
            Create Goal
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {goals.map((goal, index) => {
        const progress = calculateProgress(goal)
        return (
          <motion.div
            key={goal.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold truncate">{goal.title}</h4>
                <p className="text-sm text-muted-foreground truncate">
                  {goal.description}
                </p>
              </div>
              <Badge variant={getPriorityColor(goal.priority)}>
                {goal.priority}
              </Badge>
            </div>
            
            {goal.targetValue && (
              <div className="space-y-2">
                <Progress value={progress} className="h-2" />
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    {goal.currentValue || 0} / {goal.targetValue} {goal.unit || ''}
                  </span>
                  <span className="font-medium">
                    {Math.round(progress)}%
                  </span>
                </div>
              </div>
            )}
            
            {goal.deadline && (
              <div className="text-xs text-muted-foreground mt-2">
                Due: {new Date(goal.deadline).toLocaleDateString()}
              </div>
            )}
          </motion.div>
        )
      })}
      
      <Link href="/goals" className="block">
        <Button variant="ghost" size="sm" className="w-full">
          View all goals
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </Link>
    </div>
  )
}
