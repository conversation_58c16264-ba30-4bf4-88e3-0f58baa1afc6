
import { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Target, 
  CheckSquare, 
  Timer, 
  BarChart3, 
  BookOpen, 
  Plus,
  TrendingUp,
  Calendar,
  Clock
} from 'lucide-react'
import Link from 'next/link'
import { DashboardStats } from '@/components/dashboard-stats'
import { QuickActions } from '@/components/quick-actions'
import { LifeBalanceWidget } from '@/components/life-balance-widget'
import { TodaysTasks } from '@/components/todays-tasks'
import { GoalProgress } from '@/components/goal-progress'
import { Skeleton } from '@/components/ui/skeleton'

export default function Dashboard() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Welcome to TimeMaster
          </h1>
          <p className="text-muted-foreground mt-2">
            Your productivity dashboard based on <PERSON>'s 8 principles for time efficiency
          </p>
        </div>
        <QuickActions />
      </div>

      {/* Stats Overview */}
      <Suspense fallback={<DashboardStatsSkeleton />}>
        <DashboardStats />
      </Suspense>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Today's Priorities */}
        <Card className="xl:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle className="text-xl flex items-center space-x-2">
                <CheckSquare className="w-5 h-5 text-primary" />
                <span>Today's Priorities</span>
              </CardTitle>
              <CardDescription>
                Focus on what matters most today
              </CardDescription>
            </div>
            <Link href="/tasks">
              <Button variant="outline" size="sm">
                View All Tasks
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<TodaysTasksSkeleton />}>
              <TodaysTasks />
            </Suspense>
          </CardContent>
        </Card>

        {/* Life Balance Snapshot */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              <span>Life Balance</span>
            </CardTitle>
            <CardDescription>
              Keep your life in balance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<LifeBalanceSkeleton />}>
              <LifeBalanceWidget />
            </Suspense>
            <Link href="/analytics" className="block mt-4">
              <Button variant="outline" size="sm" className="w-full">
                View Analytics
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Goal Progress */}
        <Card className="xl:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle className="text-xl flex items-center space-x-2">
                <Target className="w-5 h-5 text-primary" />
                <span>Active Goals</span>
              </CardTitle>
              <CardDescription>
                Track progress on your key objectives
              </CardDescription>
            </div>
            <Link href="/goals">
              <Button variant="outline" size="sm">
                Manage Goals
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<GoalProgressSkeleton />}>
              <GoalProgress />
            </Suspense>
          </CardContent>
        </Card>

        {/* Focus Session Summary */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl flex items-center space-x-2">
              <Timer className="w-5 h-5 text-primary" />
              <span>Focus Sessions</span>
            </CardTitle>
            <CardDescription>
              Concentration power today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Today</span>
                <span className="text-2xl font-bold count-up">0</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Minutes</span>
                <span className="text-lg font-semibold">0</span>
              </div>
              <Link href="/focus">
                <Button className="w-full">
                  <Timer className="w-4 h-4 mr-2" />
                  Start Focus Session
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Brian Tracy's 8 Principles */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">The 8 Principles of Time Efficiency</CardTitle>
          <CardDescription>
            Master these principles to double your productivity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {principles.map((principle, index) => (
              <div key={index} className="text-center p-4 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto mb-2 text-sm font-bold">
                  {index + 1}
                </div>
                <h4 className="font-semibold text-sm mb-1">{principle.title}</h4>
                <p className="text-xs text-muted-foreground">{principle.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

const principles = [
  {
    title: "Clear Specific Goals",
    description: "Define and track your objectives"
  },
  {
    title: "Clear Specific Plans", 
    description: "Create actionable steps"
  },
  {
    title: "Make Lists",
    description: "Daily, weekly, monthly organization"
  },
  {
    title: "Set Priorities",
    description: "Focus on what matters most"
  },
  {
    title: "Concentration of Power",
    description: "Single-task focus"
  },
  {
    title: "Overcome Procrastination",
    description: "Start tasks promptly"
  },
  {
    title: "Eliminate Time Thieves",
    description: "Remove time-wasting activities"
  },
  {
    title: "Keep Life in Balance",
    description: "Maintain equilibrium across life areas"
  }
]

function DashboardStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Skeleton className="h-24" />
      <Skeleton className="h-24" />
      <Skeleton className="h-24" />
      <Skeleton className="h-24" />
    </div>
  )
}

function TodaysTasksSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10" />
      <Skeleton className="h-10" />
      <Skeleton className="h-10" />
    </div>
  )
}

function LifeBalanceSkeleton() {
  return (
    <div className="space-y-2">
      <Skeleton className="h-40" />
    </div>
  )
}

function GoalProgressSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-12" />
      <Skeleton className="h-12" />
    </div>
  )
}
