'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Clock, Database, Server, Zap } from 'lucide-react'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  details?: any
}

export default function TestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Database Connection', status: 'pending', message: 'Testing...' },
    { name: 'API Endpoints', status: 'pending', message: 'Testing...' },
    { name: 'Data Retrieval', status: 'pending', message: 'Testing...' },
    { name: 'Dashboard Stats', status: 'pending', message: 'Testing...' },
    { name: 'Goals API', status: 'pending', message: 'Testing...' },
    { name: 'Tasks API', status: 'pending', message: 'Testing...' }
  ])

  const [isRunning, setIsRunning] = useState(false)

  const updateTest = (index: number, status: TestResult['status'], message: string, details?: any) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, details } : test
    ))
  }

  const runTests = async () => {
    setIsRunning(true)
    
    // Test 1: Database Connection
    try {
      const response = await fetch('/api/test')
      const data = await response.json()
      if (data.status === 'success') {
        updateTest(0, 'success', 'Database connected successfully', data.data)
      } else {
        updateTest(0, 'error', data.message || 'Database connection failed')
      }
    } catch (error) {
      updateTest(0, 'error', 'Failed to connect to database')
    }

    // Test 2: API Endpoints
    try {
      const response = await fetch('/api/dashboard/stats')
      if (response.ok) {
        updateTest(1, 'success', 'API endpoints responding')
      } else {
        updateTest(1, 'error', `API returned ${response.status}`)
      }
    } catch (error) {
      updateTest(1, 'error', 'API endpoints not accessible')
    }

    // Test 3: Data Retrieval
    try {
      const response = await fetch('/api/dashboard/stats')
      const data = await response.json()
      updateTest(2, 'success', 'Data retrieved successfully', data)
    } catch (error) {
      updateTest(2, 'error', 'Failed to retrieve data')
    }

    // Test 4: Dashboard Stats
    try {
      const response = await fetch('/api/dashboard/stats')
      const data = await response.json()
      if (data.totalGoals !== undefined) {
        updateTest(3, 'success', `Found ${data.totalGoals} goals, ${data.todayTasks} tasks`, data)
      } else {
        updateTest(3, 'error', 'Dashboard stats format incorrect')
      }
    } catch (error) {
      updateTest(3, 'error', 'Dashboard stats failed')
    }

    // Test 5: Goals API
    try {
      const response = await fetch('/api/goals')
      const data = await response.json()
      if (Array.isArray(data)) {
        updateTest(4, 'success', `Retrieved ${data.length} goals`, data.slice(0, 3))
      } else {
        updateTest(4, 'error', 'Goals API returned invalid format')
      }
    } catch (error) {
      updateTest(4, 'error', 'Goals API failed')
    }

    // Test 6: Tasks API
    try {
      const response = await fetch('/api/tasks')
      const data = await response.json()
      if (Array.isArray(data)) {
        updateTest(5, 'success', `Retrieved ${data.length} tasks`, data.slice(0, 3))
      } else {
        updateTest(5, 'error', 'Tasks API returned invalid format')
      }
    } catch (error) {
      updateTest(5, 'error', 'Tasks API failed')
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">TimeMaster App Test Suite</h1>
          <p className="text-muted-foreground mt-2">
            Verify that all core functionality is working properly
          </p>
        </div>
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          <Zap className="h-4 w-4" />
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </Button>
      </div>

      <div className="grid gap-4">
        {tests.map((test, index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <CardTitle className="text-lg">{test.name}</CardTitle>
                </div>
                {getStatusBadge(test.status)}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2">{test.message}</p>
              {test.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm font-medium">View Details</summary>
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(test.details, null, 2)}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Test individual components and features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" size="sm" asChild>
              <a href="/" target="_blank">Dashboard</a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href="/goals" target="_blank">Goals</a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href="/tasks" target="_blank">Tasks</a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href="/journal" target="_blank">Journal</a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href="/focus" target="_blank">Focus</a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href="/analytics" target="_blank">Analytics</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}