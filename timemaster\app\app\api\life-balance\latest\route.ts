
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const assessment = await prisma.lifeBalanceAssessment.findFirst({
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(assessment)
  } catch (error) {
    console.error('Get life balance error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch life balance assessment' },
      { status: 500 }
    )
  }
}
