'use client'

import React, { useState, useEffect } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  BarChart3,
  PieChart,
  TrendingUp,
  Clock,
  Target,
  Activity,
  Plus,
  Save
} from 'lucide-react'
import { motion } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import { timeLogSchema, lifeBalanceSchema, validateFormData, getValidationErrorMessage } from '@/lib/validations'

// @ts-ignore - Recharts type compatibility issue
import {
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie as RechartsPie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar as RechartsRadar
} from 'recharts';
interface TimeLogData {
  category: string
  duration: number
  date: string
}

interface LifeBalanceData {
  area: string
  current: number
  target: number
}

export default function AnalyticsClient() {
  const [activeTab, setActiveTab] = useState('time-tracking')
  const [timeLogData, setTimeLogData] = useState<TimeLogData[]>([])
  const [lifeBalanceData, setLifeBalanceData] = useState<LifeBalanceData[]>([])
  const [loading, setLoading] = useState(true)
  const [showNewLogForm, setShowNewLogForm] = useState(false)
  const [showNewAssessmentForm, setShowNewAssessmentForm] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchAnalyticsData()
  }, [])

  const fetchAnalyticsData = async () => {
    try {
      // Fetch recent time logs
      const timeLogsResponse = await fetch('/api/time-logs')
      if (timeLogsResponse.ok) {
        const logs = await timeLogsResponse.json()
        
        // Group by category for chart
        const categoryData = logs.reduce((acc: any, log: any) => {
          const existing = acc.find((item: any) => item.category === log.category)
          if (existing) {
            existing.duration += log.duration
          } else {
            acc.push({
              category: log.category,
              duration: log.duration,
              date: log.date
            })
          }
          return acc
        }, [])
        
        setTimeLogData(categoryData)
      }

      // Fetch latest life balance assessment
      const balanceResponse = await fetch('/api/life-balance/latest')
      if (balanceResponse.ok) {
        const assessment = await balanceResponse.json()
        if (assessment) {
          const areas = [
            { area: 'Career', current: assessment.career, target: assessment.careerTarget },
            { area: 'Finance', current: assessment.finances, target: assessment.financesTarget },
            { area: 'Health', current: assessment.health, target: assessment.healthTarget },
            { area: 'Family', current: assessment.family, target: assessment.familyTarget },
            { area: 'Social', current: assessment.social, target: assessment.socialTarget },
            { area: 'Personal', current: assessment.personal, target: assessment.personalTarget },
            { area: 'Recreation', current: assessment.recreation, target: assessment.recreationTarget },
            { area: 'Environment', current: assessment.environment, target: assessment.environmentTarget }
          ]
          setLifeBalanceData(areas)
        } else {
          // Set default data if no assessment exists
          const defaultAreas = [
            { area: 'Career', current: 5, target: 8 },
            { area: 'Finance', current: 5, target: 8 },
            { area: 'Health', current: 5, target: 8 },
            { area: 'Family', current: 5, target: 8 },
            { area: 'Social', current: 5, target: 8 },
            { area: 'Personal', current: 5, target: 8 },
            { area: 'Recreation', current: 5, target: 8 },
            { area: 'Environment', current: 5, target: 8 }
          ]
          setLifeBalanceData(defaultAreas)
        }
      }
    } catch (error) {
      console.error('Failed to fetch analytics data:', error)
      toast({
        title: "Error",
        description: "Failed to fetch analytics data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTimeLog = async (formData: any) => {
    try {
      const response = await fetch('/api/time-logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Time log created successfully"
        })
        setShowNewLogForm(false)
        fetchAnalyticsData()
      }
    } catch (error) {
      console.error('Failed to create time log:', error)
      toast({
        title: "Error",
        description: "Failed to create time log",
        variant: "destructive"
      })
    }
  }

  const handleCreateAssessment = async (formData: any) => {
    try {
      const response = await fetch('/api/life-balance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Life balance assessment saved successfully"
        })
        setShowNewAssessmentForm(false)
        fetchAnalyticsData()
      }
    } catch (error) {
      console.error('Failed to create assessment:', error)
      toast({
        title: "Error",
        description: "Failed to save assessment",
        variant: "destructive"
      })
    }
  }

  // Chart colors
  const chartColors = ['#60B5FF', '#FF9149', '#FF9898', '#FF90BB', '#FF6363', '#80D8C3', '#A19AD3', '#72BF78']

  if (loading) {
      return (
        <>
          <div className="space-y-6">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-96" />
          </div>
        </>
      )
    }

    return (
      <>
        <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
            <BarChart3 className="w-8 h-8 text-primary" />
            <span>Analytics</span>
          </h1>
          <p className="text-muted-foreground mt-2">
            Track your time usage and life balance
          </p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="time-tracking">Time Tracking</TabsTrigger>
          <TabsTrigger value="life-balance">Life Balance</TabsTrigger>
        </TabsList>

        {/* Time Tracking Tab */}
        <TabsContent value="time-tracking" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Time Usage Analysis</h2>
            <Button onClick={() => setShowNewLogForm(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Log Activity
            </Button>
          </div>

          {showNewLogForm && (
            <TimeLogForm 
              onSubmit={handleCreateTimeLog}
              onCancel={() => setShowNewLogForm(false)}
            />
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Time Distribution Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Time Distribution by Category</CardTitle>
                <CardDescription>
                  How you spend your time across different activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={timeLogData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="category"
                        tick={{ fontSize: 10 }}
                        tickLine={false}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        tick={{ fontSize: 10 }}
                        tickLine={false}
                        label={{
                          value: 'Minutes',
                          angle: -90,
                          position: 'insideLeft',
                          style: { textAnchor: 'middle', fontSize: 11 }
                        }}
                      />
                      <Tooltip
                        formatter={(value: any) => [`${value} minutes`, 'Duration']}
                        labelStyle={{ fontSize: 11 }}
                      />
                      <Bar
                        dataKey="duration"
                        fill="hsl(var(--primary))"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Time Distribution Pie Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Time Allocation</CardTitle>
                <CardDescription>
                  Percentage breakdown of your activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <RechartsPie
                        data={timeLogData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="duration"
                      >
                        {timeLogData.map((entry, index) => (
                          <Cell key={`cell-${index}-${entry.category}`} fill={chartColors[index % chartColors.length]} />
                        ))}
                      </RechartsPie>
                      <Tooltip
                        formatter={(value) => [`${value} minutes`, 'Duration']}
                        labelStyle={{ fontSize: 11 }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Time Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Time Insights</CardTitle>
              <CardDescription>
                Identify patterns and opportunities for improvement
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Clock className="w-8 h-8 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold">
                    {timeLogData.reduce((total, item) => total + item.duration, 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Minutes Logged</div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <Activity className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold">
                    {timeLogData.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Activity Categories</div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <TrendingUp className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold">
                    {timeLogData.length > 0 
                      ? Math.round(timeLogData.reduce((total, item) => total + item.duration, 0) / timeLogData.length)
                      : 0
                    }
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Minutes per Activity</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Life Balance Tab */}
        <TabsContent value="life-balance" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Life Balance Assessment</h2>
            <Button onClick={() => setShowNewAssessmentForm(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Assessment
            </Button>
          </div>

          {showNewAssessmentForm && (
            <LifeBalanceForm 
              onSubmit={handleCreateAssessment}
              onCancel={() => setShowNewAssessmentForm(false)}
            />
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Life Balance Wheel */}
            <Card>
              <CardHeader>
                <CardTitle>Wheel of Life</CardTitle>
                <CardDescription>
                  Visual representation of your life balance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={lifeBalanceData}>
                      <PolarGrid />
                      <PolarAngleAxis
                        dataKey="area"
                        tick={{ fontSize: 9 }}
                        tickFormatter={(value) => value.length > 8 ? value.substring(0, 8) + '...' : value}
                      />
                      <PolarRadiusAxis
                        angle={90}
                        domain={[0, 10]}
                        tick={{ fontSize: 8 }}
                      />
                      <RechartsRadar
                        name="Current"
                        dataKey="current"
                        stroke="hsl(var(--primary))"
                        fill="hsl(var(--primary))"
                        fillOpacity={0.3}
                        strokeWidth={2}
                      />
                      <RechartsRadar
                        name="Target"
                        dataKey="target"
                        stroke="hsl(var(--muted-foreground))"
                        fill="transparent"
                        strokeWidth={1}
                        strokeDasharray="5 5"
                      />
                      <Legend
                        verticalAlign="top"
                        wrapperStyle={{ fontSize: 11 }}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Balance Scores */}
            <Card>
              <CardHeader>
                <CardTitle>Balance Scores</CardTitle>
                <CardDescription>
                  Current vs target scores for each life area
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {lifeBalanceData.map((area, index) => (
                    <div key={area.area.toString()} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{area.area}</span>
                        <div className="flex space-x-2">
                          <Badge variant="outline">
                            {area.current}/10
                          </Badge>
                          <Badge variant="secondary">
                            Target: {area.target}/10
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all"
                          style={{ width: `${(area.current / 10) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Balance Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Balance Insights</CardTitle>
              <CardDescription>
                Areas for improvement and strengths
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-red-600">Areas Needing Attention</h4>
                  <div className="space-y-2">
                    {lifeBalanceData
                      .filter(area => area.current < area.target - 1)
                      .sort((a, b) => (a.target - a.current) - (b.target - b.current))
                      .slice(0, 3)
                      .map(area => (
                        <div key={area.area} className="flex justify-between items-center p-2 bg-red-50 rounded">
                          <span className="text-sm">{area.area}</span>
                          <Badge variant="destructive">
                            -{(area.target - area.current)} points
                          </Badge>
                        </div>
                      ))
                    }
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-3 text-green-600">Strong Areas</h4>
                  <div className="space-y-2">
                    {lifeBalanceData
                      .filter(area => area.current >= area.target)
                      .sort((a, b) => b.current - a.current)
                      .slice(0, 3)
                      .map(area => (
                        <div key={area.area} className="flex justify-between items-center p-2 bg-green-50 rounded">
                          <span className="text-sm">{area.area}</span>
                          <Badge variant="success">
                            {area.current >= area.target ? 'On Target' : `+${area.current - area.target}`}
                          </Badge>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
        </div>
      </>
    )
}

function TimeLogForm({ onSubmit, onCancel }: {
  onSubmit: (data: any) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    activity: '',
    category: '',
    duration: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    const data = {
      ...formData,
      duration: formData.duration ? parseInt(formData.duration) : 0,
      date: new Date(formData.date)
    }

    const validation = validateFormData(timeLogSchema, data)

    if (!validation.success) {
      setErrors(validation.errors)
      toast({
        title: "Validation Error",
        description: getValidationErrorMessage(validation.errors),
        variant: "destructive"
      })
      setIsSubmitting(false)
      return
    }

    try {
      await onSubmit(validation.data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save time log",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Log Time Activity</CardTitle>
        <CardDescription>
          Record how you spent your time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="activity">Activity *</Label>
              <Input
                id="activity"
                value={formData.activity}
                onChange={(e) => {
                  setFormData({...formData, activity: e.target.value})
                  if (errors.activity) {
                    setErrors(prev => ({ ...prev, activity: '' }))
                  }
                }}
                placeholder="e.g., Reading, Exercise, Meetings"
                className={errors.activity ? 'border-destructive' : ''}
              />
              {errors.activity && (
                <p className="text-sm text-destructive mt-1">{errors.activity}</p>
              )}
            </div>

            <div>
              <Label htmlFor="category">Category *</Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => {
                  setFormData({...formData, category: e.target.value})
                  if (errors.category) {
                    setErrors(prev => ({ ...prev, category: '' }))
                  }
                }}
                placeholder="e.g., Work, Health, Learning"
                className={errors.category ? 'border-destructive' : ''}
              />
              {errors.category && (
                <p className="text-sm text-destructive mt-1">{errors.category}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="duration">Duration (minutes) *</Label>
              <Input
                id="duration"
                type="number"
                min="1"
                max="1440"
                value={formData.duration}
                onChange={(e) => {
                  setFormData({...formData, duration: e.target.value})
                  if (errors.duration) {
                    setErrors(prev => ({ ...prev, duration: '' }))
                  }
                }}
                placeholder="60"
                className={errors.duration ? 'border-destructive' : ''}
              />
              {errors.duration && (
                <p className="text-sm text-destructive mt-1">{errors.duration}</p>
              )}
            </div>

            <div>
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => {
                  setFormData({...formData, date: e.target.value})
                  if (errors.date) {
                    setErrors(prev => ({ ...prev, date: '' }))
                  }
                }}
                className={errors.date ? 'border-destructive' : ''}
              />
              {errors.date && (
                <p className="text-sm text-destructive mt-1">{errors.date}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => {
                setFormData({...formData, notes: e.target.value})
                if (errors.notes) {
                  setErrors(prev => ({ ...prev, notes: '' }))
                }
              }}
              placeholder="Additional details about this activity..."
              rows={3}
              className={errors.notes ? 'border-destructive' : ''}
            />
            {errors.notes && (
              <p className="text-sm text-destructive mt-1">{errors.notes}</p>
            )}
          </div>

          <div className="flex space-x-2">
            <Button type="submit" disabled={isSubmitting}>
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Log'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function LifeBalanceForm({ onSubmit, onCancel }: {
  onSubmit: (data: any) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    career: 5,
    finances: 5,
    health: 5,
    family: 5,
    social: 5,
    personal: 5,
    recreation: 5,
    environment: 5,
    careerTarget: 8,
    financesTarget: 8,
    healthTarget: 8,
    familyTarget: 8,
    socialTarget: 8,
    personalTarget: 8,
    recreationTarget: 8,
    environmentTarget: 8,
    notes: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    const validation = validateFormData(lifeBalanceSchema, formData)

    if (!validation.success) {
      setErrors(validation.errors)
      toast({
        title: "Validation Error",
        description: getValidationErrorMessage(validation.errors),
        variant: "destructive"
      })
      setIsSubmitting(false)
      return
    }

    try {
      await onSubmit(validation.data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save life balance assessment",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const areas = [
    { key: 'career', label: 'Career' },
    { key: 'finances', label: 'Finances' },
    { key: 'health', label: 'Health' },
    { key: 'family', label: 'Family' },
    { key: 'social', label: 'Social' },
    { key: 'personal', label: 'Personal Growth' },
    { key: 'recreation', label: 'Recreation' },
    { key: 'environment', label: 'Environment' }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Life Balance Assessment</CardTitle>
        <CardDescription>
          Rate each area of your life from 1-10 and set targets
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {areas.map(area => (
              <div key={area.key} className="space-y-3">
                <h4 className="font-medium">{area.label}</h4>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor={`${area.key}-current`}>Current (1-10)</Label>
                    <Input
                      id={`${area.key}-current`}
                      type="number"
                      min="1"
                      max="10"
                      value={formData[area.key as keyof typeof formData]}
                      onChange={(e) => {
                        const value = parseInt(e.target.value) || 1
                        setFormData({
                          ...formData,
                          [area.key]: Math.min(Math.max(value, 1), 10)
                        })
                        if (errors[area.key]) {
                          setErrors(prev => ({ ...prev, [area.key]: '' }))
                        }
                      }}
                      className={errors[area.key] ? 'border-destructive' : ''}
                    />
                    {errors[area.key] && (
                      <p className="text-sm text-destructive mt-1">{errors[area.key]}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor={`${area.key}-target`}>Target (1-10)</Label>
                    <Input
                      id={`${area.key}-target`}
                      type="number"
                      min="1"
                      max="10"
                      value={formData[`${area.key}Target` as keyof typeof formData]}
                      onChange={(e) => {
                        const value = parseInt(e.target.value) || 1
                        const targetKey = `${area.key}Target`
                        setFormData({
                          ...formData,
                          [targetKey]: Math.min(Math.max(value, 1), 10)
                        })
                        if (errors[targetKey]) {
                          setErrors(prev => ({ ...prev, [targetKey]: '' }))
                        }
                      }}
                      className={errors[`${area.key}Target`] ? 'border-destructive' : ''}
                    />
                    {errors[`${area.key}Target`] && (
                      <p className="text-sm text-destructive mt-1">{errors[`${area.key}Target`]}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => {
                setFormData({...formData, notes: e.target.value})
                if (errors.notes) {
                  setErrors(prev => ({ ...prev, notes: '' }))
                }
              }}
              placeholder="Reflections on your current life balance..."
              rows={3}
              className={errors.notes ? 'border-destructive' : ''}
            />
            {errors.notes && (
              <p className="text-sm text-destructive mt-1">{errors.notes}</p>
            )}
          </div>

          <div className="flex space-x-2">
            <Button type="submit" disabled={isSubmitting}>
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Assessment'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
