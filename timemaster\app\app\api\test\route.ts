import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    // Test database connection
    const goalCount = await prisma.goal.count()
    const taskCount = await prisma.task.count()
    const journalCount = await prisma.journalEntry.count()
    
    return NextResponse.json({
      status: 'success',
      message: 'Database connection working!',
      data: {
        goals: goalCount,
        tasks: taskCount,
        journalEntries: journalCount,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      status: 'error',
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}