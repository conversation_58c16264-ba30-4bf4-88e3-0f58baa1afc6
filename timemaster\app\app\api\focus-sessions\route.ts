
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = searchParams.get('limit')
    const date = searchParams.get('date')

    const where: any = {}
    
    if (date) {
      const targetDate = new Date(date)
      const startOfDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate())
      const endOfDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate() + 1)
      where.startedAt = { gte: startOfDay, lt: endOfDay }
    }

    const sessions = await prisma.focusSession.findMany({
      where,
      orderBy: { startedAt: 'desc' },
      take: limit ? parseInt(limit) : undefined,
      include: {
        task: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(sessions)
  } catch (error) {
    console.error('Get focus sessions error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch focus sessions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const session = await prisma.focusSession.create({
      data: {
        duration: data.duration,
        sessionType: data.sessionType || 'FOCUS',
        startedAt: new Date(data.startedAt),
        completedAt: data.completedAt ? new Date(data.completedAt) : null,
        notes: data.notes,
        taskId: data.taskId
      },
      include: {
        task: { select: { id: true, title: true } }
      }
    })

    return NextResponse.json(session, { status: 201 })
  } catch (error) {
    console.error('Create focus session error:', error)
    return NextResponse.json(
      { error: 'Failed to create focus session' },
      { status: 500 }
    )
  }
}
