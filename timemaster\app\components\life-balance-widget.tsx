
'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts'

interface LifeBalanceData {
  area: string
  current: number
  target: number
}

export function LifeBalanceWidget() {
  const [data, setData] = useState<LifeBalanceData[]>([])
  const [loading, setLoading] = useState(true)
  const [averageScore, setAverageScore] = useState(0)

  useEffect(() => {
    fetchLifeBalance()
  }, [])

  const fetchLifeBalance = async () => {
    try {
      const response = await fetch('/api/life-balance/latest')
      if (response.ok) {
        const assessment = await response.json()
        if (assessment) {
          const areas = [
            { area: 'Career', current: assessment.career, target: assessment.careerTarget },
            { area: 'Finance', current: assessment.finances, target: assessment.financesTarget },
            { area: 'Health', current: assessment.health, target: assessment.healthTarget },
            { area: 'Family', current: assessment.family, target: assessment.familyTarget },
            { area: 'Social', current: assessment.social, target: assessment.socialTarget },
            { area: 'Personal', current: assessment.personal, target: assessment.personalTarget },
            { area: 'Recreation', current: assessment.recreation, target: assessment.recreationTarget },
            { area: 'Environment', current: assessment.environment, target: assessment.environmentTarget }
          ]
          setData(areas)
          
          const avg = areas.reduce((sum, area) => sum + area.current, 0) / areas.length
          setAverageScore(Math.round(avg * 10)) // Convert to percentage
        } else {
          // Default data if no assessment exists
          const defaultAreas = [
            { area: 'Career', current: 5, target: 8 },
            { area: 'Finance', current: 5, target: 8 },
            { area: 'Health', current: 5, target: 8 },
            { area: 'Family', current: 5, target: 8 },
            { area: 'Social', current: 5, target: 8 },
            { area: 'Personal', current: 5, target: 8 },
            { area: 'Recreation', current: 5, target: 8 },
            { area: 'Environment', current: 5, target: 8 }
          ]
          setData(defaultAreas)
          setAverageScore(50)
        }
      }
    } catch (error) {
      console.error('Failed to fetch life balance:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="h-48 flex items-center justify-center">
        <div className="animate-pulse text-muted-foreground">Loading balance...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="text-center">
        <div className="text-3xl font-bold count-up">{averageScore}%</div>
        <div className="text-sm text-muted-foreground">Overall Balance Score</div>
      </div>
      
      <div className="h-40">
        <ResponsiveContainer width="100%" height="100%">
          <RadarChart data={data}>
            <PolarGrid />
            <PolarAngleAxis 
              dataKey="area" 
              tick={{ fontSize: 10 }}
            />
            <PolarRadiusAxis 
              angle={90} 
              domain={[0, 10]} 
              tick={{ fontSize: 8 }}
            />
            <Radar
              name="Current"
              dataKey="current"
              stroke="hsl(var(--primary))"
              fill="hsl(var(--primary))"
              fillOpacity={0.3}
              strokeWidth={2}
            />
            <Radar
              name="Target"
              dataKey="target"
              stroke="hsl(var(--muted-foreground))"
              fill="transparent"
              strokeWidth={1}
              strokeDasharray="5 5"
            />
          </RadarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
