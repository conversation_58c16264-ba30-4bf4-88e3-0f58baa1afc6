
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Target, 
  Plus, 
  Edit, 
  Trash2, 
  Calendar,
  CheckSquare,
  TrendingUp,
  MapPin
} from 'lucide-react'
import { Goal } from '@/lib/types'
import { motion } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import { goalSchema, validateFormData, getValidationErrorMessage } from '@/lib/validations'
import { Skeleton } from '@/components/ui/skeleton'

export default function GoalsPage() {
  const [goals, setGoals] = useState<Goal[]>([])
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null)
  const [loading, setLoading] = useState(true)
  const [showNewGoalForm, setShowNewGoalForm] = useState(false)
  const [filter, setFilter] = useState('all')
  const { toast } = useToast()

  useEffect(() => {
    fetchGoals()
  }, [])

  const fetchGoals = async () => {
    try {
      const response = await fetch('/api/goals')
      if (response.ok) {
        const data = await response.json()
        setGoals(data)
        if (data.length > 0 && !selectedGoal) {
          setSelectedGoal(data[0])
        }
      }
    } catch (error) {
      console.error('Failed to fetch goals:', error)
      toast({
        title: "Error",
        description: "Failed to fetch goals",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateGoal = async (formData: any) => {
    try {
      const response = await fetch('/api/goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const newGoal = await response.json()
        setGoals(prev => [newGoal, ...prev])
        setSelectedGoal(newGoal)
        setShowNewGoalForm(false)
        toast({
          title: "Success",
          description: "Goal created successfully"
        })
      }
    } catch (error) {
      console.error('Failed to create goal:', error)
      toast({
        title: "Error",
        description: "Failed to create goal",
        variant: "destructive"
      })
    }
  }

  const calculateProgress = (goal: Goal) => {
    if (!goal.targetValue || !goal.currentValue) return 0
    return Math.min((goal.currentValue / goal.targetValue) * 100, 100)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'destructive'
      case 'MEDIUM': return 'warning'
      case 'LOW': return 'secondary'
      default: return 'secondary'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success'
      case 'ACTIVE': return 'default'
      case 'PAUSED': return 'warning'
      case 'CANCELLED': return 'destructive'
      default: return 'secondary'
    }
  }

  const filteredGoals = goals.filter(goal => {
    if (filter === 'all') return true
    return goal.status === filter.toUpperCase()
  })

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-1/3" />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1 space-y-4">
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
          </div>
          <div className="lg:col-span-2">
            <Skeleton className="h-96" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
            <Target className="w-8 h-8 text-primary" />
            <span>Goals</span>
          </h1>
          <p className="text-muted-foreground mt-2">
            Set clear, specific goals and track your progress
          </p>
        </div>
        <Button onClick={() => setShowNewGoalForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          New Goal
        </Button>
      </div>

      {/* Filter Tabs */}
      <Tabs value={filter} onValueChange={setFilter}>
        <TabsList>
          <TabsTrigger value="all">All Goals</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="paused">Paused</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Goals List */}
        <div className="lg:col-span-1 space-y-4">
          {filteredGoals.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-semibold mb-2">No goals found</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Create your first goal to get started
                </p>
                <Button onClick={() => setShowNewGoalForm(true)} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Goal
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredGoals.map((goal, index) => (
              <motion.div
                key={goal.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedGoal?.id === goal.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedGoal(goal)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-semibold truncate flex-1">{goal.title}</h4>
                      <div className="flex space-x-1 ml-2">
                        <Badge variant={getPriorityColor(goal.priority)}>
                          {goal.priority}
                        </Badge>
                        <Badge variant={getStatusColor(goal.status)}>
                          {goal.status}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {goal.description}
                    </p>
                    
                    {goal.targetValue && (
                      <div className="space-y-2">
                        <Progress value={calculateProgress(goal)} className="h-2" />
                        <div className="flex justify-between text-xs">
                          <span className="text-muted-foreground">
                            {goal.currentValue || 0} / {goal.targetValue} {goal.unit || ''}
                          </span>
                          <span className="font-medium">
                            {Math.round(calculateProgress(goal))}%
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {goal.deadline && (
                      <div className="flex items-center text-xs text-muted-foreground mt-2">
                        <Calendar className="w-3 h-3 mr-1" />
                        Due: {new Date(goal.deadline).toLocaleDateString()}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </div>

        {/* Goal Details */}
        <div className="lg:col-span-2">
          {showNewGoalForm ? (
            <NewGoalForm 
              onSubmit={handleCreateGoal}
              onCancel={() => setShowNewGoalForm(false)}
            />
          ) : selectedGoal ? (
            <GoalDetails 
              goal={selectedGoal}
              onUpdate={fetchGoals}
            />
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Target className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select a goal</h3>
                <p className="text-muted-foreground">
                  Choose a goal from the list to view details and track progress
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

function NewGoalForm({ onSubmit, onCancel }: {
  onSubmit: (data: any) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'personal',
    priority: 'MEDIUM',
    deadline: '',
    targetValue: '',
    unit: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    const data = {
      ...formData,
      deadline: formData.deadline ? new Date(formData.deadline) : null,
      targetValue: formData.targetValue ? parseFloat(formData.targetValue) : null
    }

    const validation = validateFormData(goalSchema, data)

    if (!validation.success) {
      setErrors(validation.errors)
      toast({
        title: "Validation Error",
        description: getValidationErrorMessage(validation.errors),
        variant: "destructive"
      })
      setIsSubmitting(false)
      return
    }

    try {
      await onSubmit(validation.data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create goal",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Goal</CardTitle>
        <CardDescription>
          Set a clear, specific goal with measurable outcomes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Goal Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => {
                setFormData({...formData, title: e.target.value})
                if (errors.title) {
                  setErrors(prev => ({ ...prev, title: '' }))
                }
              }}
              placeholder="e.g., Lose 20 pounds"
              className={errors.title ? 'border-destructive' : ''}
            />
            {errors.title && (
              <p className="text-sm text-destructive mt-1">{errors.title}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => {
                setFormData({...formData, description: e.target.value})
                if (errors.description) {
                  setErrors(prev => ({ ...prev, description: '' }))
                }
              }}
              placeholder="Describe your goal in detail..."
              rows={3}
              className={errors.description ? 'border-destructive' : ''}
            />
            {errors.description && (
              <p className="text-sm text-destructive mt-1">{errors.description}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Category</Label>
              <Select 
                value={formData.category}
                onValueChange={(value) => setFormData({...formData, category: value})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">Personal</SelectItem>
                  <SelectItem value="career">Career</SelectItem>
                  <SelectItem value="health">Health</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="relationships">Relationships</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select 
                value={formData.priority}
                onValueChange={(value) => setFormData({...formData, priority: value})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="deadline">Deadline</Label>
            <Input
              id="deadline"
              type="date"
              value={formData.deadline}
              onChange={(e) => {
                setFormData({...formData, deadline: e.target.value})
                if (errors.deadline) {
                  setErrors(prev => ({ ...prev, deadline: '' }))
                }
              }}
              className={errors.deadline ? 'border-destructive' : ''}
            />
            {errors.deadline && (
              <p className="text-sm text-destructive mt-1">{errors.deadline}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="targetValue">Target Value</Label>
              <Input
                id="targetValue"
                type="number"
                min="0.01"
                step="0.01"
                value={formData.targetValue}
                onChange={(e) => {
                  setFormData({...formData, targetValue: e.target.value})
                  if (errors.targetValue) {
                    setErrors(prev => ({ ...prev, targetValue: '' }))
                  }
                }}
                placeholder="e.g., 20"
                className={errors.targetValue ? 'border-destructive' : ''}
              />
              {errors.targetValue && (
                <p className="text-sm text-destructive mt-1">{errors.targetValue}</p>
              )}
            </div>

            <div>
              <Label htmlFor="unit">Unit</Label>
              <Input
                id="unit"
                value={formData.unit}
                onChange={(e) => {
                  setFormData({...formData, unit: e.target.value})
                  if (errors.unit) {
                    setErrors(prev => ({ ...prev, unit: '' }))
                  }
                }}
                placeholder="e.g., lbs, books, hours"
                className={errors.unit ? 'border-destructive' : ''}
              />
              {errors.unit && (
                <p className="text-sm text-destructive mt-1">{errors.unit}</p>
              )}
            </div>
          </div>

          <div className="flex space-x-2 pt-4">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Goal'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function GoalDetails({ goal, onUpdate }: { 
  goal: Goal
  onUpdate: () => void 
}) {
  const [isEditing, setIsEditing] = useState(false)

  const progress = goal.targetValue && goal.currentValue 
    ? Math.min((goal.currentValue / goal.targetValue) * 100, 100)
    : 0

  return (
    <Card>
      <CardHeader className="flex flex-row items-start justify-between space-y-0">
        <div className="flex-1">
          <CardTitle className="flex items-center space-x-2">
            <span>{goal.title}</span>
            <Badge variant={goal.status === 'COMPLETED' ? 'success' : 'default'}>
              {goal.status}
            </Badge>
          </CardTitle>
          <CardDescription className="mt-2">
            {goal.description}
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => {}}>
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Progress Section */}
        {goal.targetValue && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">Progress</h4>
              <span className="text-2xl font-bold">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-3" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Current: {goal.currentValue || 0} {goal.unit || ''}</span>
              <span>Target: {goal.targetValue} {goal.unit || ''}</span>
            </div>
          </div>
        )}

        {/* Info Grid */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Category:</span>
              <span className="capitalize">{goal.category}</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Priority:</span>
              <Badge variant={goal.priority === 'HIGH' ? 'destructive' : goal.priority === 'MEDIUM' ? 'warning' : 'secondary'}>
                {goal.priority}
              </Badge>
            </div>
          </div>
          <div className="space-y-2">
            {goal.deadline && (
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <span className="font-medium">Deadline:</span>
                <span>{new Date(goal.deadline).toLocaleDateString()}</span>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <CheckSquare className="w-4 h-4 text-muted-foreground" />
              <span className="font-medium">Created:</span>
              <span>{new Date(goal.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Milestones */}
        <div>
          <h4 className="font-semibold mb-3">Milestones</h4>
          {goal.milestones && goal.milestones.length > 0 ? (
            <div className="space-y-2">
              {goal.milestones.map((milestone) => (
                <div key={milestone.id} className="flex items-center space-x-3 p-2 rounded border">
                  <input
                    type="checkbox"
                    checked={milestone.completed}
                    className="rounded"
                    readOnly
                  />
                  <span className={milestone.completed ? 'line-through text-muted-foreground' : ''}>
                    {milestone.title}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No milestones created yet</p>
          )}
        </div>

        {/* Related Tasks */}
        <div>
          <h4 className="font-semibold mb-3">Related Tasks</h4>
          {goal.tasks && goal.tasks.length > 0 ? (
            <div className="space-y-2">
              {goal.tasks.slice(0, 5).map((task) => (
                <div key={task.id} className="flex items-center space-x-3 p-2 rounded border">
                  <CheckSquare className="w-4 h-4 text-muted-foreground" />
                  <span className="flex-1">{task.title}</span>
                  <Badge variant={task.status === 'COMPLETED' ? 'success' : 'secondary'}>
                    {task.status}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No related tasks yet</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
