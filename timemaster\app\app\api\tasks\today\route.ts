
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    const tasks = await prisma.task.findMany({
      where: {
        OR: [
          { dueDate: { gte: startOfDay, lt: endOfDay } },
          { timeFrame: 'DAILY', createdAt: { gte: startOfDay } }
        ]
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' }
      ],
      take: 10,
      include: {
        goal: { select: { title: true } }
      }
    })

    return NextResponse.json(tasks)
  } catch (error) {
    console.error('Today tasks error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch today\'s tasks' },
      { status: 500 }
    )
  }
}
