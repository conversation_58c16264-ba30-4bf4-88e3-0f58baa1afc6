
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "sqlite"
    url      = "file:./dev.db"
}

model Goal {
  id          String   @id @default(cuid())
  title       String
  description String?
  category    String   // career, personal, health, financial, etc.
  priority    Priority @default(MEDIUM)
  status      GoalStatus @default(ACTIVE)
  deadline    DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Progress tracking
  targetValue    Float?
  currentValue   Float? @default(0)
  unit          String? // e.g., "lbs", "books", "hours"
  
  // Relationships
  milestones    Milestone[]
  plans         Plan[]
  tasks         Task[]
  journalEntries JournalEntry[]
  
  @@map("goals")
}

model Milestone {
  id          String   @id @default(cuid())
  title       String
  description String?
  completed   Boolean  @default(false)
  deadline    DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  
  // Relationships
  goalId      String
  goal        Goal     @relation(fields: [goalId], references: [id], onDelete: Cascade)
  
  @@map("milestones")
}

model Plan {
  id          String   @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  goalId      String
  goal        Goal     @relation(fields: [goalId], references: [id], onDelete: Cascade)
  actionSteps ActionStep[]
  
  @@map("plans")
}

model ActionStep {
  id          String   @id @default(cuid())
  title       String
  description String?
  completed   Boolean  @default(false)
  order       Int
  deadline    DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  
  // Relationships
  planId      String
  plan        Plan     @relation(fields: [planId], references: [id], onDelete: Cascade)
  
  @@map("action_steps")
}

model Task {
  id          String     @id @default(cuid())
  title       String
  description String?
  priority    Priority   @default(MEDIUM)
  status      TaskStatus @default(PENDING)
  dueDate     DateTime?
  completedAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  // Task categorization
  category    String?    // work, personal, health, etc.
  timeFrame   TimeFrame  @default(DAILY) // daily, weekly, monthly
  
  // Recurring task support
  isRecurring Boolean    @default(false)
  recurrencePattern String? // "daily", "weekly", "monthly", etc.
  
  // Eisenhower Matrix
  isUrgent    Boolean    @default(false)
  isImportant Boolean    @default(true)
  
  // Estimated and actual time
  estimatedMinutes Int?
  actualMinutes    Int?
  
  // Relationships
  goalId      String?
  goal        Goal?      @relation(fields: [goalId], references: [id], onDelete: SetNull)
  focusSessions FocusSession[]
  journalEntries JournalEntry[]
  
  @@map("tasks")
}

model FocusSession {
  id           String    @id @default(cuid())
  duration     Int       // duration in minutes
  sessionType  SessionType @default(FOCUS)
  startedAt    DateTime
  completedAt  DateTime?
  notes        String?
  createdAt    DateTime  @default(now())
  
  // Relationships
  taskId       String?
  task         Task?     @relation(fields: [taskId], references: [id], onDelete: SetNull)
  
  @@map("focus_sessions")
}

model TimeLog {
  id          String   @id @default(cuid())
  activity    String
  category    String   // work, leisure, exercise, learning, etc.
  duration    Int      // duration in minutes
  date        DateTime
  notes       String?
  createdAt   DateTime @default(now())
  
  @@map("time_logs")
}

model LifeBalanceAssessment {
  id          String   @id @default(cuid())
  date        DateTime @default(now())
  
  // Life areas (scale 1-10)
  career      Int      @default(5)
  finances    Int      @default(5)
  health      Int      @default(5)
  family      Int      @default(5)
  social      Int      @default(5)
  personal    Int      @default(5)
  recreation  Int      @default(5)
  environment Int      @default(5)
  
  // Target values
  careerTarget      Int @default(8)
  financesTarget    Int @default(8)
  healthTarget      Int @default(8)
  familyTarget      Int @default(8)
  socialTarget      Int @default(8)
  personalTarget    Int @default(8)
  recreationTarget  Int @default(8)
  environmentTarget Int @default(8)
  
  notes       String?
  createdAt   DateTime @default(now())
  
  @@map("life_balance_assessments")
}

model JournalEntry {
  id          String   @id @default(cuid())
  title       String?
  content     String
  date        DateTime @default(now())
  mood        String?  // happy, neutral, stressed, etc.
  tags        String?  @default("") // Changed from String[] to String (comma-separated)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  goalId      String?
  goal        Goal?    @relation(fields: [goalId], references: [id], onDelete: SetNull)
  taskId      String?
  task        Task?    @relation(fields: [taskId], references: [id], onDelete: SetNull)
  
  @@map("journal_entries")
}

model ProcrastinationLog {
  id          String   @id @default(cuid())
  taskTitle   String
  reason      String   // "overwhelming", "unclear", "boring", etc.
  strategy    String?  // what helped overcome it
  timeWasted  Int?     // minutes procrastinated
  date        DateTime @default(now())
  createdAt   DateTime @default(now())
  
  @@map("procrastination_logs")
}

model Setting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  updatedAt   DateTime @updatedAt
  
  @@map("settings")
}

// Enums
enum Priority {
  LOW
  MEDIUM
  HIGH
}

enum GoalStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TimeFrame {
  DAILY
  WEEKLY
  MONTHLY
}

enum SessionType {
  FOCUS
  SHORT_BREAK
  LONG_BREAK
}
