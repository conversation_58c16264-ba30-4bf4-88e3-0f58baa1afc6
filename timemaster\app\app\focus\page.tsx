
'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  Timer, 
  Play, 
  Pause, 
  Square, 
  RotateCcw,
  Coffee,
  Brain,
  Clock,
  CheckSquare
} from 'lucide-react'
import { SessionType, Task } from '@/lib/types'
import { motion } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'

import { Skeleton } from '@/components/ui/skeleton'

export default function FocusPage() {
  const [time, setTime] = useState(25 * 60) // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false)
  const [sessionType, setSessionType] = useState<SessionType>('FOCUS')
  const [selectedTask, setSelectedTask] = useState<string>('')
  const [tasks, setTasks] = useState<Task[]>([])
  const [sessions, setSessions] = useState<any[]>([])
  const [notes, setNotes] = useState('')
  const [sessionCount, setSessionCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const intervalRef = useRef<NodeJS.Timeout>()
  const startTimeRef = useRef<Date>()
  const { toast } = useToast()

  // Session durations in minutes
  const sessionDurations = {
    FOCUS: 25,
    SHORT_BREAK: 5,
    LONG_BREAK: 15
  }

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      await Promise.all([fetchTasks(), fetchTodaySessions()])
      setLoading(false)
    }
    fetchData()
  }, [])

  useEffect(() => {
    setTime(sessionDurations[sessionType] * 60)
  }, [sessionType])

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTime(prev => {
          if (prev <= 1) {
            handleSessionComplete()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRunning])

  const fetchTasks = async () => {
    try {
      const response = await fetch('/api/tasks?status=PENDING')
      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
    }
  }

  const fetchTodaySessions = async () => {
    try {
      const today = new Date().toISOString().split('T')[0]
      const response = await fetch(`/api/focus-sessions?date=${today}`)
      if (response.ok) {
        const data = await response.json()
        setSessions(data)

        // Count completed focus sessions today
        const focusSessions = data.filter((s: any) => s.sessionType === 'FOCUS' && s.completedAt)
        setSessionCount(focusSessions.length)
      }
    } catch (error) {
      console.error('Failed to fetch sessions:', error)
    }
  }

  const handleStart = () => {
    setIsRunning(true)
    startTimeRef.current = new Date()
  }

  const handlePause = () => {
    setIsRunning(false)
  }

  const handleReset = () => {
    setIsRunning(false)
    setTime(sessionDurations[sessionType] * 60)
    setNotes('')
  }

  const handleSessionComplete = async () => {
    setIsRunning(false)
    
    if (startTimeRef.current) {
      try {
        const sessionData = {
          duration: sessionDurations[sessionType],
          sessionType,
          startedAt: startTimeRef.current.toISOString(),
          completedAt: new Date().toISOString(),
          notes,
          taskId: selectedTask || null
        }

        const response = await fetch('/api/focus-sessions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(sessionData)
        })

        if (response.ok) {
          toast({
            title: "Session Complete!",
            description: `${sessionType} session completed successfully`
          })
          
          // Auto-suggest next session
          if (sessionType === 'FOCUS') {
            const newCount = sessionCount + 1
            setSessionCount(newCount)
            if (newCount % 4 === 0) {
              setSessionType('LONG_BREAK')
              toast({
                title: "Time for a long break!",
                description: "You've completed 4 focus sessions"
              })
            } else {
              setSessionType('SHORT_BREAK')
              toast({
                title: "Time for a short break!",
                description: "Take a few minutes to rest"
              })
            }
          } else {
            setSessionType('FOCUS')
            toast({
              title: "Ready to focus!",
              description: "Time to get back to work"
            })
          }
          
          fetchTodaySessions()
          setNotes('')
        }
      } catch (error) {
        console.error('Failed to save session:', error)
        toast({
          title: "Error",
          description: "Failed to save session",
          variant: "destructive"
        })
      }
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getSessionIcon = (type: SessionType) => {
    switch (type) {
      case 'FOCUS': return Brain
      case 'SHORT_BREAK': return Coffee
      case 'LONG_BREAK': return Coffee
      default: return Timer
    }
  }

  const getSessionColor = (type: SessionType) => {
    switch (type) {
      case 'FOCUS': return 'text-blue-600'
      case 'SHORT_BREAK': return 'text-green-600'
      case 'LONG_BREAK': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  const progress = ((sessionDurations[sessionType] * 60 - time) / (sessionDurations[sessionType] * 60)) * 100

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="text-center">
          <Skeleton className="h-9 w-48 mx-auto" />
          <Skeleton className="h-5 w-80 mx-auto mt-2" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-80" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-40" />
            <Skeleton className="h-64" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground flex items-center justify-center space-x-2">
          <Timer className="w-8 h-8 text-primary" />
          <span>Focus Timer</span>
        </h1>
        <p className="text-muted-foreground mt-2">
          Use the Pomodoro technique to boost your concentration power
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Timer */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
            <CardContent className="p-8 text-center">
              {/* Session Type Selector */}
              <div className="flex justify-center space-x-2 mb-8">
                {Object.entries(sessionDurations).map(([type, duration]) => {
                  const Icon = getSessionIcon(type as SessionType)
                  const isActive = sessionType === type
                  return (
                    <Button
                      key={type}
                      variant={isActive ? "default" : "outline"}
                      onClick={() => !isRunning && setSessionType(type as SessionType)}
                      disabled={isRunning}
                      className="flex items-center space-x-2"
                    >
                      <Icon className="w-4 h-4" />
                      <span className="capitalize">
                        {type.replace('_', ' ')} ({duration}m)
                      </span>
                    </Button>
                  )
                })}
              </div>

              {/* Timer Display */}
              <div className="relative w-64 h-64 mx-auto mb-8">
                <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="transparent"
                    className="text-muted"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="transparent"
                    strokeDasharray={`${2 * Math.PI * 45}`}
                    strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}
                    className="text-primary transition-all duration-1000 ease-linear"
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="text-4xl font-bold font-mono">
                    {formatTime(time)}
                  </div>
                  <div className="text-sm text-muted-foreground capitalize">
                    {sessionType.replace('_', ' ')}
                  </div>
                </div>
              </div>

              {/* Timer Controls */}
              <div className="flex justify-center space-x-4">
                {!isRunning ? (
                  <Button onClick={handleStart} size="lg" className="px-8">
                    <Play className="w-5 h-5 mr-2" />
                    Start
                  </Button>
                ) : (
                  <Button onClick={handlePause} size="lg" variant="secondary" className="px-8">
                    <Pause className="w-5 h-5 mr-2" />
                    Pause
                  </Button>
                )}
                
                <Button onClick={handleReset} size="lg" variant="outline">
                  <RotateCcw className="w-5 h-5 mr-2" />
                  Reset
                </Button>
                
                <Button onClick={handleSessionComplete} size="lg" variant="outline">
                  <Square className="w-5 h-5 mr-2" />
                  Stop
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Session Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Session Details</CardTitle>
              <CardDescription>
                Configure your focus session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="task">Focus on Task (Optional)</Label>
                <Select value={selectedTask} onValueChange={setSelectedTask}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a task to focus on" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-task">No specific task</SelectItem>
                    {tasks.map(task => (
                      <SelectItem key={task.id} value={task.id}>
                        {task.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="notes">Session Notes</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="What did you accomplish? Any insights or challenges?"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Today's Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Today's Progress</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">{sessionCount}</div>
                <div className="text-sm text-muted-foreground">Focus Sessions</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-semibold">
                  {sessions.reduce((total, session) => 
                    session.sessionType === 'FOCUS' && session.completedAt 
                      ? total + session.duration 
                      : total, 0
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Minutes Focused</div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Next break in:</span>
                  <span className="font-medium">
                    {4 - (sessionCount % 4)} sessions
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{ width: `${(sessionCount % 4) * 25}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              {sessions.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No sessions today. Start your first focus session!
                </p>
              ) : (
                <div className="space-y-3">
                  {sessions.slice(0, 5).map((session, index) => {
                    const Icon = getSessionIcon(session.sessionType)
                    return (
                      <motion.div
                        key={session.id}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center space-x-3 p-2 rounded border"
                      >
                        <Icon className={`w-4 h-4 ${getSessionColor(session.sessionType)}`} />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium capitalize">
                            {session.sessionType.replace('_', ' ')}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {session.duration}m • {new Date(session.startedAt).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                        {session.completedAt && (
                          <Badge variant="success">
                            Complete
                          </Badge>
                        )}
                      </motion.div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pomodoro Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Pomodoro Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start space-x-2">
                <Brain className="w-4 h-4 text-blue-600 mt-0.5" />
                <p>During focus sessions, eliminate all distractions and work on one task only.</p>
              </div>
              <div className="flex items-start space-x-2">
                <Coffee className="w-4 h-4 text-green-600 mt-0.5" />
                <p>Use breaks to rest your mind - step away from screens if possible.</p>
              </div>
              <div className="flex items-start space-x-2">
                <Clock className="w-4 h-4 text-purple-600 mt-0.5" />
                <p>After 4 focus sessions, take a longer 15-30 minute break.</p>
              </div>
              <div className="flex items-start space-x-2">
                <CheckSquare className="w-4 h-4 text-orange-600 mt-0.5" />
                <p>Review what you accomplished after each session.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
