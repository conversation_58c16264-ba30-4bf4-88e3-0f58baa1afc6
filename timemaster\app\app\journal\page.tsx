
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BookOpen, 
  Plus, 
  Search,
  Calendar,
  Tag,
  Edit,
  Trash2,
  Save,
  Target,
  CheckSquare
} from 'lucide-react'
import { JournalEntry, Goal, Task } from '@/lib/types'
import { motion } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import { journalEntrySchema, validateFormData, getValidationErrorMessage } from '@/lib/validations'
import { useSearchParams } from 'next/navigation'
import { tagsToArray, arrayToTags } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'

export default function JournalPage() {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [selectedEntry, setSelectedEntry] = useState<JournalEntry | null>(null)
  const [goals, setGoals] = useState<Goal[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [showNewEntryForm, setShowNewEntryForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedMood, setSelectedMood] = useState('')
  const [selectedTag, setSelectedTag] = useState('')
  const { toast } = useToast()
  const searchParams = useSearchParams()

  useEffect(() => {
    fetchEntries()
    fetchGoals()
    fetchTasks()
    
    // Check if new entry form should be shown
    if (searchParams.get('new') === 'true') {
      setShowNewEntryForm(true)
    }
  }, [searchParams])

  const fetchEntries = async () => {
    try {
      const response = await fetch('/api/journal')
      if (response.ok) {
        const data = await response.json()
        setEntries(data)
        if (data.length > 0 && !selectedEntry) {
          setSelectedEntry(data[0])
        }
      }
    } catch (error) {
      console.error('Failed to fetch journal entries:', error)
      toast({
        title: "Error",
        description: "Failed to fetch journal entries",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchGoals = async () => {
    try {
      const response = await fetch('/api/goals?status=ACTIVE')
      if (response.ok) {
        const data = await response.json()
        setGoals(data)
      }
    } catch (error) {
      console.error('Failed to fetch goals:', error)
    }
  }

  const fetchTasks = async () => {
    try {
      const response = await fetch('/api/tasks?status=PENDING')
      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
    }
  }

  const handleCreateEntry = async (formData: any) => {
    try {
      const response = await fetch('/api/journal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const newEntry = await response.json()
        setEntries(prev => [newEntry, ...prev])
        setSelectedEntry(newEntry)
        setShowNewEntryForm(false)
        toast({
          title: "Success",
          description: "Journal entry created successfully"
        })
      }
    } catch (error) {
      console.error('Failed to create journal entry:', error)
      toast({
        title: "Error",
        description: "Failed to create journal entry",
        variant: "destructive"
      })
    }
  }

  const handleUpdateEntry = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/journal/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const updatedEntry = await response.json()
        setEntries(prev => prev.map(entry => 
          entry.id === id ? updatedEntry : entry
        ))
        setSelectedEntry(updatedEntry)
        toast({
          title: "Success",
          description: "Journal entry updated successfully"
        })
      }
    } catch (error) {
      console.error('Failed to update journal entry:', error)
      toast({
        title: "Error",
        description: "Failed to update journal entry",
        variant: "destructive"
      })
    }
  }

  const handleDeleteEntry = async (id: string) => {
    if (!confirm('Are you sure you want to delete this journal entry?')) return
    
    try {
      const response = await fetch(`/api/journal/${id}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setEntries(prev => prev.filter(entry => entry.id !== id))
        setSelectedEntry(null)
        toast({
          title: "Success",
          description: "Journal entry deleted successfully"
        })
      }
    } catch (error) {
      console.error('Failed to delete journal entry:', error)
      toast({
        title: "Error",
        description: "Failed to delete journal entry",
        variant: "destructive"
      })
    }
  }

  const getMoodColor = (mood: string | undefined) => {
    if (!mood) return 'bg-gray-100 text-gray-800'
    
    switch (mood.toLowerCase()) {
      case 'happy': return 'bg-green-100 text-green-800'
      case 'excited': return 'bg-yellow-100 text-yellow-800'
      case 'neutral': return 'bg-gray-100 text-gray-800'
      case 'stressed': return 'bg-orange-100 text-orange-800'
      case 'sad': return 'bg-blue-100 text-blue-800'
      case 'angry': return 'bg-red-100 text-red-800'
      case 'anxious': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get all unique tags from entries (convert strings to arrays first)
  const allTags = Array.from(new Set(entries.flatMap(entry => tagsToArray(entry.tags))))
  const allMoods = Array.from(new Set(entries.map(entry => entry.mood).filter(Boolean)))

  // Filter entries based on search and filters
  const filteredEntries = entries.filter(entry => {
    // Search term matching in title and content
    const matchesSearch = !searchTerm ||
      entry.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.content.toLowerCase().includes(searchTerm.toLowerCase())

    // Mood filtering - pass if no mood selected or 'all' selected or moods match
    const matchesMood = selectedMood === 'all' || !selectedMood || entry.mood === selectedMood

    // Tag filtering - pass if no tag selected or 'all' selected or entry has the selected tag
    const entryTags = tagsToArray(entry.tags)
    const matchesTag = selectedTag === 'all' || !selectedTag || entryTags.includes(selectedTag)

    return matchesSearch && matchesMood && matchesTag
  })

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-1/3" />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1 space-y-4">
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
          </div>
          <div className="lg:col-span-2">
            <Skeleton className="h-96" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
            <BookOpen className="w-8 h-8 text-primary" />
            <span>Journal</span>
          </h1>
          <p className="text-muted-foreground mt-2">
            Record your thoughts, reflections, and insights
          </p>
        </div>
        <Button onClick={() => setShowNewEntryForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          New Entry
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search entries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedMood} onValueChange={setSelectedMood}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by mood" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All moods</SelectItem>
                {allMoods.map(mood => (
                  <SelectItem key={mood} value={mood || "unknown"}>
                    <span className="capitalize">{mood}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedTag} onValueChange={setSelectedTag}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by tag" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All tags</SelectItem>
                {allTags.map(tag => (
                  <SelectItem key={tag} value={tag}>
                    {tag}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Entries List */}
        <div className="lg:col-span-1 space-y-4">
          {filteredEntries.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-semibold mb-2">No entries found</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {entries.length === 0 
                    ? "Start by creating your first journal entry"
                    : "Try adjusting your search or filters"
                  }
                </p>
                {entries.length === 0 && (
                  <Button onClick={() => setShowNewEntryForm(true)} size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Entry
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            filteredEntries.map((entry, index) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedEntry?.id === entry.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedEntry(entry)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <h4 className="font-semibold truncate flex-1">
                          {entry.title || 'Untitled Entry'}
                        </h4>
                        {entry.mood && (
                          <Badge className={getMoodColor(entry.mood)}>
                            {entry.mood}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {entry.content}
                      </p>

                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>
                            {new Date(entry.date).toLocaleDateString()}
                          </span>
                        </div>

                        {tagsToArray(entry.tags).length > 0 && (
                          <div className="flex items-center space-x-1">
                            <Tag className="w-3 h-3" />
                            <span>{tagsToArray(entry.tags).length} tags</span>
                          </div>
                        )}
                      </div>

                      {tagsToArray(entry.tags).length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {tagsToArray(entry.tags).slice(0, 3).map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {tagsToArray(entry.tags).length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{tagsToArray(entry.tags).length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </div>

        {/* Entry Details */}
        <div className="lg:col-span-2">
          {showNewEntryForm ? (
            <JournalEntryForm 
              goals={goals}
              tasks={tasks}
              onSubmit={handleCreateEntry}
              onCancel={() => setShowNewEntryForm(false)}
            />
          ) : selectedEntry ? (
            <JournalEntryDetail 
              entry={selectedEntry}
              goals={goals}
              tasks={tasks}
              onUpdate={handleUpdateEntry}
              onDelete={handleDeleteEntry}
            />
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <BookOpen className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select an entry</h3>
                <p className="text-muted-foreground">
                  Choose an entry from the list to view and edit it
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

function JournalEntryForm({ goals, tasks, onSubmit, onCancel, entry }: {
  goals: Goal[]
  tasks: Task[]
  onSubmit: (data: any) => void
  onCancel: () => void
  entry?: JournalEntry
}) {
  const [formData, setFormData] = useState({
    title: entry?.title || '',
    content: entry?.content || '',
    mood: entry?.mood || 'none',
    tags: entry?.tags || '',
    goalId: entry?.goalId || 'none',
    taskId: entry?.taskId || 'none'
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    const data = {
      ...formData,
      mood: formData.mood === 'none' ? undefined : formData.mood,
      tags: formData.tags,
      goalId: formData.goalId === 'none' ? null : formData.goalId || null,
      taskId: formData.taskId === 'none' ? null : formData.taskId || null
    }

    const validation = validateFormData(journalEntrySchema, data)

    if (!validation.success) {
      setErrors(validation.errors)
      toast({
        title: "Validation Error",
        description: getValidationErrorMessage(validation.errors),
        variant: "destructive"
      })
      setIsSubmitting(false)
      return
    }

    try {
      await onSubmit(validation.data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save journal entry",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const moods = ['happy', 'excited', 'neutral', 'focused', 'stressed', 'tired', 'anxious', 'sad', 'angry', 'grateful']

  return (
    <Card>
      <CardHeader>
        <CardTitle>{entry ? 'Edit Entry' : 'New Journal Entry'}</CardTitle>
        <CardDescription>
          Record your thoughts, reflections, and insights
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Title (Optional)</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => {
                setFormData({...formData, title: e.target.value})
                if (errors.title) {
                  setErrors(prev => ({ ...prev, title: '' }))
                }
              }}
              placeholder="Entry title..."
              className={errors.title ? 'border-destructive' : ''}
            />
            {errors.title && (
              <p className="text-sm text-destructive mt-1">{errors.title}</p>
            )}
          </div>

          <div>
            <Label htmlFor="content">Content *</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => {
                setFormData({...formData, content: e.target.value})
                if (errors.content) {
                  setErrors(prev => ({ ...prev, content: '' }))
                }
              }}
              placeholder="What's on your mind? Share your thoughts, reflections, or insights..."
              rows={8}
              className={errors.content ? 'border-destructive' : ''}
            />
            {errors.content && (
              <p className="text-sm text-destructive mt-1">{errors.content}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="mood">Mood</Label>
              <Select 
                value={formData.mood}
                onValueChange={(value) => setFormData({...formData, mood: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="How are you feeling?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No mood selected</SelectItem>
                  {moods.map(mood => (
                    <SelectItem key={mood} value={mood}>
                      <span className="capitalize">{mood}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                value={formData.tags}
                onChange={(e) => {
                  setFormData({...formData, tags: e.target.value})
                  if (errors.tags) {
                    setErrors(prev => ({ ...prev, tags: '' }))
                  }
                }}
                placeholder="work, reflection, idea (comma separated)"
                className={errors.tags ? 'border-destructive' : ''}
              />
              {errors.tags && (
                <p className="text-sm text-destructive mt-1">{errors.tags}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="goalId">Related Goal (Optional)</Label>
              <Select
                value={formData.goalId}
                onValueChange={(value) => setFormData({...formData, goalId: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Link to a goal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No goal</SelectItem>
                  {goals.map(goal => (
                    <SelectItem key={goal.id} value={goal.id}>
                      {goal.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="taskId">Related Task (Optional)</Label>
              <Select
                value={formData.taskId}
                onValueChange={(value) => setFormData({...formData, taskId: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Link to a task" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No task</SelectItem>
                  {tasks.map(task => (
                    <SelectItem key={task.id} value={task.id}>
                      {task.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex space-x-2 pt-4">
            <Button type="submit" disabled={isSubmitting}>
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Saving...' : (entry ? 'Update Entry' : 'Save Entry')}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function JournalEntryDetail({ entry, goals, tasks, onUpdate, onDelete }: { 
  entry: JournalEntry
  goals: Goal[]
  tasks: Task[]
  onUpdate: (id: string, data: any) => void
  onDelete: (id: string) => void
}) {
  const [isEditing, setIsEditing] = useState(false)

  const handleUpdate = (data: any) => {
    onUpdate(entry.id, data)
    setIsEditing(false)
  }

  const getMoodColor = (mood: string | undefined) => {
    if (!mood) return 'bg-gray-100 text-gray-800'
    
    switch (mood.toLowerCase()) {
      case 'happy': return 'bg-green-100 text-green-800'
      case 'excited': return 'bg-yellow-100 text-yellow-800'
      case 'neutral': return 'bg-gray-100 text-gray-800'
      case 'focused': return 'bg-blue-100 text-blue-800'
      case 'stressed': return 'bg-orange-100 text-orange-800'
      case 'tired': return 'bg-purple-100 text-purple-800'
      case 'anxious': return 'bg-red-100 text-red-800'
      case 'sad': return 'bg-blue-200 text-blue-900'
      case 'angry': return 'bg-red-100 text-red-800'
      case 'grateful': return 'bg-green-200 text-green-900'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isEditing) {
    return (
      <JournalEntryForm 
        entry={entry}
        goals={goals}
        tasks={tasks}
        onSubmit={handleUpdate}
        onCancel={() => setIsEditing(false)}
      />
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-start justify-between space-y-0">
        <div className="flex-1">
          <CardTitle className="flex items-center space-x-2">
            <span>{entry.title || 'Untitled Entry'}</span>
            {entry.mood && (
              <Badge className={`capitalize ${getMoodColor(entry.mood)}`}>
                {entry.mood}
              </Badge>
            )}
          </CardTitle>
          <CardDescription className="flex items-center space-x-4 mt-2">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{new Date(entry.date).toLocaleDateString()}</span>
            </div>
            <span>•</span>
            <span>
              {new Date(entry.createdAt).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => onDelete(entry.id)}>
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Content */}
        <div className="prose prose-sm max-w-none">
          <div className="whitespace-pre-wrap">{entry.content}</div>
        </div>

        {/* Tags */}
        {tagsToArray(entry.tags).length > 0 && (
          <div>
            <h4 className="font-semibold mb-2 flex items-center space-x-2">
              <Tag className="w-4 h-4" />
              <span>Tags</span>
            </h4>
            <div className="flex flex-wrap gap-2">
              {tagsToArray(entry.tags).map(tag => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Related Goal/Task */}
        {(entry.goalId || entry.taskId) && (
          <div className="space-y-3">
            <h4 className="font-semibold">Related Items</h4>
            
            {entry.goalId && (
              <div className="flex items-center space-x-2 p-2 border rounded">
                <Target className="w-4 h-4 text-blue-600" />
                <span className="text-sm">
                  Goal: {goals.find(g => g.id === entry.goalId)?.title || 'Unknown Goal'}
                </span>
              </div>
            )}
            
            {entry.taskId && (
              <div className="flex items-center space-x-2 p-2 border rounded">
                <CheckSquare className="w-4 h-4 text-green-600" />
                <span className="text-sm">
                  Task: {tasks.find(t => t.id === entry.taskId)?.title || 'Unknown Task'}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Metadata */}
        <div className="text-xs text-muted-foreground border-t pt-4">
          <div className="flex justify-between">
            <span>Created: {new Date(entry.createdAt).toLocaleString()}</span>
            <span>Updated: {new Date(entry.updatedAt).toLocaleString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
