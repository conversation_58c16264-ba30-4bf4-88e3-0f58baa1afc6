
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Clock,
  Target,
  Activity,
  Plus,
  Save
} from 'lucide-react'
import { motion } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts'
import { Skeleton } from '@/components/ui/skeleton'

interface TimeLogData {
  category: string
  duration: number
  date: string
}

interface LifeBalanceData {
  area: string
  current: number
  target: number
}

import dynamic from 'next/dynamic'

const AnalyticsClient = dynamic(() => import('@/components/client/AnalyticsClient'), { ssr: false })

export default function AnalyticsPage() {
  return <AnalyticsClient />
}

function TimeLogForm({ onSubmit, onCancel }: { 
  onSubmit: (data: any) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState({
    activity: '',
    category: '',
    duration: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const data = {
      ...formData,
      duration: parseInt(formData.duration),
      date: new Date(formData.date)
    }
    onSubmit(data)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Log Time Activity</CardTitle>
        <CardDescription>
          Record how you spent your time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="activity">Activity *</Label>
              <Input
                id="activity"
                value={formData.activity}
                onChange={(e) => setFormData({...formData, activity: e.target.value})}
                placeholder="e.g., Reading, Exercise, Meetings"
                required
              />
            </div>

            <div>
              <Label htmlFor="category">Category *</Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                placeholder="e.g., Work, Health, Learning"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="duration">Duration (minutes) *</Label>
              <Input
                id="duration"
                type="number"
                value={formData.duration}
                onChange={(e) => setFormData({...formData, duration: e.target.value})}
                placeholder="60"
                required
              />
            </div>

            <div>
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({...formData, date: e.target.value})}
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              placeholder="Additional details about this activity..."
              rows={3}
            />
          </div>

          <div className="flex space-x-2">
            <Button type="submit">
              <Save className="w-4 h-4 mr-2" />
              Save Log
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function LifeBalanceForm({ onSubmit, onCancel }: { 
  onSubmit: (data: any) => void
  onCancel: () => void 
}) {
  const [formData, setFormData] = useState({
    career: 5,
    finances: 5,
    health: 5,
    family: 5,
    social: 5,
    personal: 5,
    recreation: 5,
    environment: 5,
    careerTarget: 8,
    financesTarget: 8,
    healthTarget: 8,
    familyTarget: 8,
    socialTarget: 8,
    personalTarget: 8,
    recreationTarget: 8,
    environmentTarget: 8,
    notes: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const areas = [
    { key: 'career', label: 'Career' },
    { key: 'finances', label: 'Finances' },
    { key: 'health', label: 'Health' },
    { key: 'family', label: 'Family' },
    { key: 'social', label: 'Social' },
    { key: 'personal', label: 'Personal Growth' },
    { key: 'recreation', label: 'Recreation' },
    { key: 'environment', label: 'Environment' }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Life Balance Assessment</CardTitle>
        <CardDescription>
          Rate each area of your life from 1-10 and set targets
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {areas.map(area => (
              <div key={area.key} className="space-y-3">
                <h4 className="font-medium">{area.label}</h4>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor={`${area.key}-current`}>Current (1-10)</Label>
                    <Input
                      id={`${area.key}-current`}
                      type="number"
                      min="1"
                      max="10"
                      value={formData[area.key as keyof typeof formData]}
                      onChange={(e) => setFormData({
                        ...formData, 
                        [area.key]: parseInt(e.target.value)
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`${area.key}-target`}>Target (1-10)</Label>
                    <Input
                      id={`${area.key}-target`}
                      type="number"
                      min="1"
                      max="10"
                      value={formData[`${area.key}Target` as keyof typeof formData]}
                      onChange={(e) => setFormData({
                        ...formData, 
                        [`${area.key}Target`]: parseInt(e.target.value)
                      })}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              placeholder="Reflections on your current life balance..."
              rows={3}
            />
          </div>

          <div className="flex space-x-2">
            <Button type="submit">
              <Save className="w-4 h-4 mr-2" />
              Save Assessment
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
