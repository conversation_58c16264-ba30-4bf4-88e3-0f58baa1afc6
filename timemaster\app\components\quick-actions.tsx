
'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Plus, Timer, BookOpen } from 'lucide-react'
import Link from 'next/link'

export function QuickActions() {
  return (
    <div className="flex space-x-2">
      <Link href="/tasks?new=true">
        <Button size="sm">
          <Plus className="w-4 h-4 mr-2" />
          New Task
        </Button>
      </Link>
      <Link href="/focus">
        <Button variant="outline" size="sm">
          <Timer className="w-4 h-4 mr-2" />
          Focus Session
        </Button>
      </Link>
      <Link href="/journal?new=true">
        <Button variant="outline" size="sm">
          <BookOpen className="w-4 h-4 mr-2" />
          Journal Entry
        </Button>
      </Link>
    </div>
  )
}
