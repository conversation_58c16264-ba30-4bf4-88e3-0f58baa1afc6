
'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Target, CheckSquare, Timer, TrendingUp } from 'lucide-react'
import { motion } from 'framer-motion'

interface Stats {
  totalGoals: number
  activeGoals: number
  completedGoals: number
  todayTasks: number
  completedTasks: number
  focusMinutes: number
  lifeBalanceScore: number
}

export function DashboardStats() {
  const [stats, setStats] = useState<Stats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error)
      // Set default stats on error
      setStats({
        totalGoals: 0,
        activeGoals: 0,
        completedGoals: 0,
        todayTasks: 0,
        completedTasks: 0,
        focusMinutes: 0,
        lifeBalanceScore: 50
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-8 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) return null

  const statCards = [
    {
      title: "Active Goals",
      value: stats.activeGoals,
      subtitle: `${stats.totalGoals} total`,
      icon: Target,
      color: "text-blue-600"
    },
    {
      title: "Today's Tasks",
      value: stats.todayTasks,
      subtitle: `${stats.completedTasks} completed`,
      icon: CheckSquare,
      color: "text-green-600"
    },
    {
      title: "Focus Minutes",
      value: stats.focusMinutes,
      subtitle: "Today",
      icon: Timer,
      color: "text-purple-600"
    },
    {
      title: "Life Balance",
      value: `${stats.lifeBalanceScore}%`,
      subtitle: "Overall score",
      icon: TrendingUp,
      color: "text-orange-600"
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => {
        const Icon = stat.icon
        return (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold count-up">
                      {stat.value}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {stat.subtitle}
                    </p>
                  </div>
                  <Icon className={`w-8 h-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )
      })}
    </div>
  )
}
