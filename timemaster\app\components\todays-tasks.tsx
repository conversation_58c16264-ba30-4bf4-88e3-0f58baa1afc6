
'use client'

import { useEffect, useState } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Clock, ArrowRight, CheckSquare, Plus } from 'lucide-react'
import { Task } from '@/lib/types'
import { motion } from 'framer-motion'
import Link from 'next/link'

export function TodaysTasks() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTodaysTasks()
  }, [])

  const fetchTodaysTasks = async () => {
    try {
      const response = await fetch('/api/tasks/today')
      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      }
    } catch (error) {
      console.error('Failed to fetch today\'s tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTaskToggle = async (taskId: string, completed: boolean) => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status: completed ? 'COMPLETED' : 'PENDING',
          completedAt: completed ? new Date() : null
        })
      })
      
      if (response.ok) {
        setTasks(prev => prev.map(task => 
          task.id === taskId 
            ? { ...task, status: completed ? 'COMPLETED' : 'PENDING' }
            : task
        ))
      }
    } catch (error) {
      console.error('Failed to update task:', error)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'destructive'
      case 'MEDIUM': return 'warning'
      case 'LOW': return 'secondary'
      default: return 'secondary'
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3 rounded-lg animate-pulse">
            <div className="w-4 h-4 bg-muted rounded"></div>
            <div className="flex-1">
              <div className="h-4 bg-muted rounded mb-1"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckSquare className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="font-semibold mb-2">No tasks for today</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Start by creating your first task
        </p>
        <Link href="/tasks?new=true">
          <Button size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {tasks.slice(0, 5).map((task, index) => (
        <motion.div
          key={task.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
        >
          <Checkbox
            checked={task.status === 'COMPLETED'}
            onCheckedChange={(checked) => handleTaskToggle(task.id, checked as boolean)}
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h4 className={`font-medium truncate ${
                task.status === 'COMPLETED' ? 'line-through text-muted-foreground' : ''
              }`}>
                {task.title}
              </h4>
              <Badge variant={getPriorityColor(task.priority)}>
                {task.priority}
              </Badge>
            </div>
            {task.dueDate && (
              <div className="flex items-center space-x-1 mt-1">
                <Clock className="w-3 h-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {new Date(task.dueDate).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            )}
          </div>
        </motion.div>
      ))}
      
      {tasks.length > 5 && (
        <Link href="/tasks" className="block">
          <Button variant="ghost" size="sm" className="w-full">
            View {tasks.length - 5} more tasks
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </Link>
      )}
    </div>
  )
}
