# TimeMaster Supabase Migration Guide
Complete step-by-step guide to migrate from SQLite to Supabase PostgreSQL

## Prerequisites
- Supabase account created
- TimeMaster project running locally
- Node.js and npm installed

## Step 1: Get Supabase Credentials

1. Go to your Supabase dashboard: https://app.supabase.com
2. Select your project (or create new one)
3. Go to Settings → Database
4. Copy the connection string under "Connection string" → "URI"
5. Go to Settings → API
6. Copy the Project URL and anon/public key

## Step 2: Update Environment Variables

Create/update `.env.local`:
```env
# Supabase Configuration
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
NEXT_PUBLIC_SUPABASE_URL="https://[YOUR-PROJECT-REF].supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[YOUR-ANON-KEY]"
```

**Important:** Replace `[YOUR-PASSWORD]`, `[YOUR-PROJECT-REF]`, and `[YOUR-ANON-KEY]` with actual values from your Supabase dashboard.

## Step 3: Update Prisma Schema

Update `prisma/schema.prisma`:
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Keep all your existing models (Goal, Task, etc.) - just change the datasource
```

## Step 4: Install Dependencies (if needed)

```bash
# Install Supabase client for future auth features
npm install @supabase/supabase-js

# Ensure Prisma is up to date
npm install @prisma/client
```

## Step 5: Database Migration Commands

Execute these commands in order:

```bash
# 1. Stop your development server if running
# Ctrl+C to stop npm run dev

# 2. Generate Prisma client with new schema
npx prisma generate

# 3. Push schema to Supabase (creates tables)
npx prisma db push

# 4. Verify connection works
npx prisma studio
# This should open Prisma Studio connected to your Supabase database

# 5. Seed the production database
npx tsx scripts/seed.ts
```

## Step 6: Test the Migration

```bash
# 1. Start development server
npm run dev

# 2. Test database connection
# Visit: http://localhost:3000/api/test
# Should show database stats with Supabase data

# 3. Test key functionality:
# - Create a new goal
# - Add a task
# - Start a focus session
# - Check analytics page
```

## Step 7: Production Build Test

```bash
# 1. Build for production
npm run build

# 2. Start production server
npm run start

# 3. Test all pages in production mode
# Pay special attention to /analytics page
```

## Step 8: Create Supabase Helper (Optional - Future Auth)

Create `lib/supabase.ts`:
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

## Production Deployment Checklist

### Pre-Deployment Requirements
- [ ] **Database Migration Complete**
  - [ ] Supabase project created and configured
  - [ ] DATABASE_URL working in development
  - [ ] All tables created via `npx prisma db push`
  - [ ] Production database seeded successfully
  - [ ] Prisma Studio connects to Supabase

- [ ] **Build & Testing**
  - [ ] `npm run build` completes successfully
  - [ ] Production server starts with `npm run start`
  - [ ] All pages load in production mode
  - [ ] Analytics page renders charts correctly
  - [ ] API endpoints respond properly

- [ ] **Environment Configuration**
  - [ ] Production environment variables set
  - [ ] DATABASE_URL configured for production
  - [ ] NEXT_PUBLIC_SUPABASE_URL set
  - [ ] NEXT_PUBLIC_SUPABASE_ANON_KEY set
  - [ ] No .env.local in production (use platform env vars)

### Deployment Platform Setup
- [ ] **Choose Platform** (Vercel recommended for Next.js)
  - [ ] Account created on deployment platform
  - [ ] Repository connected
  - [ ] Build settings configured

- [ ] **Environment Variables in Production**
  - [ ] DATABASE_URL added to platform
  - [ ] NEXT_PUBLIC_SUPABASE_URL added
  - [ ] NEXT_PUBLIC_SUPABASE_ANON_KEY added
  - [ ] Any other custom env vars added

### Post-Deployment Verification
- [ ] **Core Functionality**
  - [ ] Homepage loads without errors
  - [ ] Dashboard displays real data
  - [ ] Goals page CRUD operations work
  - [ ] Tasks page (Eisenhower Matrix) functional
  - [ ] Journal entries save and load
  - [ ] Focus timer records sessions
  - [ ] Analytics page renders all charts

- [ ] **API Endpoints**
  - [ ] `/api/test` returns database connection status
  - [ ] `/api/goals` returns goals data
  - [ ] `/api/tasks` and `/api/tasks/today` work
  - [ ] `/api/dashboard/stats` provides dashboard data
  - [ ] `/api/life-balance` handles assessments
  - [ ] All POST/PUT/DELETE operations function

- [ ] **Performance & UX**
  - [ ] Page load times acceptable
  - [ ] Mobile responsiveness verified
  - [ ] Form validation working
  - [ ] Toast notifications appear
  - [ ] Loading states display properly

### Production Monitoring Setup
- [ ] **Health Checks**
  - [ ] Basic uptime monitoring configured
  - [ ] Database connection monitoring
  - [ ] Error tracking setup (optional)

- [ ] **Supabase Dashboard**
  - [ ] Database usage monitored
  - [ ] Query performance reviewed
  - [ ] Backup settings verified

### Security & Maintenance
- [ ] **Database Security**
  - [ ] Row Level Security policies (if using Supabase Auth)
  - [ ] API keys secured and not exposed
  - [ ] Database access restricted

- [ ] **Code Quality**
  - [ ] No console.log statements in production
  - [ ] Error boundaries tested
  - [ ] TypeScript compilation clean

### Launch Readiness Checklist
- [ ] **Final Testing**
  - [ ] Complete user workflow tested end-to-end
  - [ ] All forms submit successfully
  - [ ] Data persistence verified
  - [ ] Analytics calculations correct

- [ ] **Documentation**
  - [ ] README updated with production info
  - [ ] Environment setup documented
  - [ ] Known issues documented

- [ ] **Backup Plan**
  - [ ] Database backup strategy confirmed
  - [ ] Rollback plan prepared
  - [ ] Contact info for support ready

## Verification Checklist

After migration, verify these work:

### Database Operations
- [ ] Goals CRUD (create, read, update, delete)
- [ ] Tasks CRUD with goal relationships
- [ ] Journal entries save and load
- [ ] Focus sessions record properly
- [ ] Time logs track correctly
- [ ] Life balance assessments save
- [ ] Procrastination logs work

### API Endpoints Test
- [ ] `/api/test` - Shows database connection
- [ ] `/api/goals` - Lists goals
- [ ] `/api/tasks` - Lists tasks
- [ ] `/api/tasks/today` - Today's tasks
- [ ] `/api/dashboard/stats` - Dashboard statistics
- [ ] `/api/life-balance` - Life balance data

### Pages Functionality
- [ ] Dashboard loads with real data
- [ ] Goals page shows Supabase data
- [ ] Tasks page (Eisenhower Matrix) works
- [ ] Analytics page renders charts
- [ ] Journal page loads entries
- [ ] Focus timer saves sessions

## Troubleshooting

### Connection Issues
```bash
# Test connection directly
npx prisma db pull
# Should succeed if connection is working
```

### Migration Errors
```bash
# Reset and try again
npx prisma migrate reset
npx prisma db push
npx tsx scripts/seed.ts
```

### SSL Certificate Issues
- Supabase requires SSL - this is handled automatically
- If issues persist, add `?sslmode=require` to DATABASE_URL

### Seed Script Issues
```bash
# If seed fails, check the data types match PostgreSQL
# Run seed with more verbose output:
npx tsx --require dotenv/config scripts/seed.ts
```

## Post-Migration Cleanup

1. **Remove SQLite files:**
   ```bash
   rm prisma/dev.db*
   ```

2. **Update .gitignore:**
   ```
   # Remove SQLite entries, keep:
   .env.local
   .env*.local
   ```

3. **Commit changes:**
   ```bash
   git add .
   git commit -m "Migrate to Supabase PostgreSQL database"
   ```

## Production Deployment Notes

### Environment Variables for Production
Ensure these are set in your production environment:
- `DATABASE_URL` (Supabase connection string)
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

### Database Migrations in Production
```bash
# For future schema changes:
npx prisma migrate deploy
```

## Success Indicators

✅ **Migration Complete When:**
- Prisma Studio shows Supabase data
- `/api/test` returns database stats
- All pages load without errors
- Production build works
- Analytics charts render properly

✅ **Production Ready When:**
- All checklist items completed
- End-to-end testing successful
- Performance acceptable
- Monitoring configured

## Next Steps After Migration

1. **Deploy to Production** (Vercel/Netlify/etc.)
2. **Add Authentication** (Supabase Auth integration)
3. **Set up Monitoring** (Supabase dashboard)
4. **Configure Backups** (automatic with Supabase)

## Support

If you encounter issues:
1. Check Supabase dashboard for connection status
2. Verify environment variables are correct
3. Test with `npx prisma studio` first
4. Check console logs for specific error messages

---

**Estimated Time:** 30-60 minutes for complete migration
**Difficulty:** Beginner-friendly with step-by-step instructions