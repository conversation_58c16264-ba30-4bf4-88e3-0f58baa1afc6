
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    // Get goals stats
    const [totalGoals, activeGoals, completedGoals] = await Promise.all([
      prisma.goal.count(),
      prisma.goal.count({ where: { status: 'ACTIVE' } }),
      prisma.goal.count({ where: { status: 'COMPLETED' } })
    ])

    // Get today's tasks stats
    const [todayTasks, completedTasks] = await Promise.all([
      prisma.task.count({
        where: {
          OR: [
            { dueDate: { gte: startOfDay, lt: endOfDay } },
            { timeFrame: 'DAILY', createdAt: { gte: startOfDay } }
          ]
        }
      }),
      prisma.task.count({
        where: {
          status: 'COMPLETED',
          completedAt: { gte: startOfDay, lt: endOfDay }
        }
      })
    ])

    // Get today's focus minutes
    const focusSessions = await prisma.focusSession.aggregate({
      where: {
        startedAt: { gte: startOfDay, lt: endOfDay },
        completedAt: { not: null }
      },
      _sum: { duration: true }
    })

    const focusMinutes = focusSessions._sum?.duration || 0

    // Get latest life balance score
    const latestAssessment = await prisma.lifeBalanceAssessment.findFirst({
      orderBy: { createdAt: 'desc' }
    })

    let lifeBalanceScore = 50 // default
    if (latestAssessment) {
      const areas = [
        latestAssessment.career,
        latestAssessment.finances,
        latestAssessment.health,
        latestAssessment.family,
        latestAssessment.social,
        latestAssessment.personal,
        latestAssessment.recreation,
        latestAssessment.environment
      ]
      lifeBalanceScore = Math.round((areas.reduce((sum, val) => sum + val, 0) / areas.length) * 10)
    }

    const stats = {
      totalGoals,
      activeGoals,
      completedGoals,
      todayTasks,
      completedTasks,
      focusMinutes,
      lifeBalanceScore
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    )
  }
}
