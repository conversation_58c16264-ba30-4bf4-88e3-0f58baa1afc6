import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testDatabase() {
  try {
    console.log('🔍 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful')
    
    // Test data retrieval
    const goalCount = await prisma.goal.count()
    const taskCount = await prisma.task.count()
    const journalCount = await prisma.journalEntry.count()
    const focusSessionCount = await prisma.focusSession.count()
    
    console.log('📊 Database Statistics:')
    console.log(`   Goals: ${goalCount}`)
    console.log(`   Tasks: ${taskCount}`)
    console.log(`   Journal Entries: ${journalCount}`)
    console.log(`   Focus Sessions: ${focusSessionCount}`)

    // Test a sample query
    const activeGoals = await prisma.goal.findMany({
      where: { status: 'ACTIVE' },
      select: { id: true, title: true, currentValue: true, targetValue: true, unit: true }
    })

    console.log('🎯 Active Goals:')
    activeGoals.forEach(goal => {
      const progress = goal.targetValue && goal.currentValue
        ? Math.round((goal.currentValue / goal.targetValue) * 100)
        : 0
      console.log(`   - ${goal.title} (${progress}% complete)`)
    })

    // Test today's tasks
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
    
    const todayTasks = await prisma.task.findMany({
      where: {
        OR: [
          { dueDate: { gte: startOfDay, lt: endOfDay } },
          { timeFrame: 'DAILY' }
        ]
      },
      select: { id: true, title: true, status: true, priority: true }
    })
    
    console.log('📋 Today\'s Tasks:')
    todayTasks.forEach(task => {
      console.log(`   - ${task.title} [${task.status}] (${task.priority} priority)`)
    })
    
    console.log('✅ Database test completed successfully!')
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()