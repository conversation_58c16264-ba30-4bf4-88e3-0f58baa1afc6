
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const assessments = await prisma.lifeBalanceAssessment.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    return NextResponse.json(assessments)
  } catch (error) {
    console.error('Get life balance assessments error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch life balance assessments' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const assessment = await prisma.lifeBalanceAssessment.create({
      data: {
        career: data.career || 5,
        finances: data.finances || 5,
        health: data.health || 5,
        family: data.family || 5,
        social: data.social || 5,
        personal: data.personal || 5,
        recreation: data.recreation || 5,
        environment: data.environment || 5,
        careerTarget: data.careerTarget || 8,
        financesTarget: data.financesTarget || 8,
        healthTarget: data.healthTarget || 8,
        familyTarget: data.familyTarget || 8,
        socialTarget: data.socialTarget || 8,
        personalTarget: data.personalTarget || 8,
        recreationTarget: data.recreationTarget || 8,
        environmentTarget: data.environmentTarget || 8,
        notes: data.notes
      }
    })

    return NextResponse.json(assessment, { status: 201 })
  } catch (error) {
    console.error('Create life balance assessment error:', error)
    return NextResponse.json(
      { error: 'Failed to create life balance assessment' },
      { status: 500 }
    )
  }
}
